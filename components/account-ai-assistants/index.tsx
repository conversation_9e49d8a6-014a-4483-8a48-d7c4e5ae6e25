import EnrichAccount from '@/components/enrich-account';
import LookalikeCompanies from '@/components/lookalike-companies';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import React from 'react';
import MagicSVG from '~/assets/magic-colorful.svg';

const AccountAiAssistants: React.FC = () => {
  const items: ItemType[] = [
    {
      key: 'lookalike-companies',
      label: <LookalikeCompanies />,
    },
    {
      key: 'enrich',
      label: <EnrichAccount />,
    },
  ];

  return (
    <Dropdown
      menu={{
        items,
      }}
    >
      <Button
        icon={
          <img src={MagicSVG} alt='magic' width={12} style={{ color: '#fff', marginTop: '-3px' }} />
        }
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        AI Assistants
        <DownOutlined />
      </Button>
    </Dropdown>
  );
};

export default AccountAiAssistants;
