import React, { type Key, useState } from 'react';

import { useMemoizedFn, useRequest } from 'ahooks';
import type { ModalProps, TableProps } from 'antd';
import { Alert, Button, Empty, Flex, message, Modal, Spin, Table, Typography } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';

import { getContactId } from '@/utils';
import { isEmpty, isEqual } from 'es-toolkit/compat';

import { enrichContact, updateContactInfo } from '../service.ts';

const enrichKeyMap = [
  {
    enrichKey: 'first_name',
    zohoKey: 'First_Name',
    label: 'First Name',
  },
  {
    enrichKey: 'last_name',
    zohoKey: 'Last_Name',
    label: 'Last Name',
  },
  {
    enrichKey: 'email',
    zohoKey: 'Email',
    label: 'Email',
  },
  {
    enrichKey: 'phone',
    zohoKey: 'Phone',
    label: 'Phone',
  },
  {
    enrichKey: 'mobile',
    zohoKey: 'Mobile',
    label: 'Mobile',
  },
  {
    enrichKey: 'title',
    zohoKey: 'Title',
    label: 'Title',
  },
  {
    enrichKey: 'department',
    zohoKey: 'Department',
    label: 'Department',
  },
  {
    enrichKey: 'function_type',
    zohoKey: 'Function_Type',
    label: 'Function Type',
  },
  {
    enrichKey: 'linkedin',
    zohoKey: 'LinkedIn',
    label: 'LinkedIn',
  },
  {
    enrichKey: 'region',
    zohoKey: 'Region',
    label: 'Region',
  },
  {
    enrichKey: 'country_territory',
    zohoKey: 'Country_Territory',
    label: 'Country/Territory',
  },
  {
    enrichKey: 'mailing_city',
    zohoKey: 'Mailing_City',
    label: 'Mailing City',
  },
  {
    enrichKey: 'mailing_state',
    zohoKey: 'Mailing_State',
    label: 'Mailing State',
  },
  {
    enrichKey: 'mailing_street',
    zohoKey: 'Mailing_Street',
    label: 'Mailing Street',
  },
  {
    enrichKey: 'mailing_zip',
    zohoKey: 'Mailing_Zip',
    label: 'Mailing Zip',
  },
  {
    enrichKey: 'secondary_email',
    zohoKey: 'Secondary_Email',
    label: 'Secondary Email',
  },
  {
    enrichKey: 'twitter',
    zohoKey: 'Twitter',
    label: 'Twitter',
  },
  {
    enrichKey: 'facebook',
    zohoKey: 'Facebook',
    label: 'Facebook',
  },
  {
    enrichKey: 'description',
    zohoKey: 'Description',
    label: 'Description',
  },
];

interface DataType {
  enrichKey: string;
  zohoKey: string;
  enrichValue: string;
  zohoValue: string;
  zohoLabel: string;
  isSame: boolean;
  key: string;
}

const generateCompareTableData = (
  enrichData: Record<string, any>,
  zohoData: Record<string, any>,
) => {
  const dataSource: DataType[] = [];

  enrichKeyMap.forEach((item) => {
    const enrichValue = enrichData?.[item.enrichKey];
    const zohoValue = zohoData?.[item.zohoKey];
    if (isEmpty(enrichValue)) {
      return;
    }
    if (!isEqual(enrichValue, zohoValue)) {
      dataSource.push({
        key: item.zohoKey,
        enrichKey: item.enrichKey,
        zohoKey: item.zohoKey,
        zohoLabel: item.label,
        enrichValue: enrichValue,
        zohoValue: zohoValue,
        isSame: false,
      });
    }
  });

  return dataSource;
};

type EnrichDataModalProps = {
  onOk?: () => void;
  onCancel?: () => void;
} & ModalProps;

const EnrichDataModal: React.FC<EnrichDataModalProps> = ({ open, onCancel, onOk }) => {
  const contactId = getContactId();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[] | undefined>([]);
  const [selectedRows, setSelectedRows] = useState<DataType[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  const {
    loading,
    data = {},
    run: enrichRun,
    cancel: handleCancel,
  } = useRequest(
    async () => {
      if (contactId) {
        const statusData = await enrichContact(contactId);

        const { latest_data, phone_info, zohoContactInfo } = statusData ?? {};
        const dataSource = generateCompareTableData(
          { ...latest_data, ...phone_info },
          zohoContactInfo || {},
        );
        if (dataSource.length > 0) {
          setSelectedRowKeys(dataSource.map((item) => item.key));
          setSelectedRows(dataSource);
        }
        return {
          ...statusData,
          dataSource,
        };
      }
    },
    {
      refreshDeps: [contactId],
    },
  );

  const { dataSource = [] } = data;

  const { loading: updating, run: handleUpdateContact } = useRequest(
    async () => {
      // 根据 selectedRowKeys 更新 contactInfo
      const contactInfo: Record<string, any> = {};
      selectedRows.forEach((item) => {
        contactInfo[item.zohoKey] = item.enrichValue;
      });

      const res = await updateContactInfo(contactId, contactInfo);
      if (res?.data?.[0]?.code === 'SUCCESS') {
        messageApi.success(
          'Update Contact Info Success, please refresh the page to see the latest data',
        );
        onOk?.();
        window.location.reload();
      } else {
        messageApi.error('Update Contact Info Failed');
      }
    },
    {
      manual: true,
      refreshDeps: [selectedRowKeys, selectedRows, contactId],
    },
  );

  const onSelectChange = (newSelectedRowKeys: Key[], selectedRows: DataType[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const onRowClick = useMemoizedFn((record: DataType) => {
    const key = record.zohoKey as Key;

    if (!selectedRowKeys) return;

    const cloneSelectedRowKeys = [...selectedRowKeys];
    let cloneSelectedRows = [...selectedRows];

    if (cloneSelectedRowKeys.includes(key)) {
      // 取消选中
      const keyIndex = cloneSelectedRowKeys.indexOf(key);
      cloneSelectedRowKeys.splice(keyIndex, 1);
      cloneSelectedRows = cloneSelectedRows.filter((row) => row.zohoKey !== key);
    } else {
      // 选中
      cloneSelectedRowKeys.push(key);
      cloneSelectedRows = [...cloneSelectedRows, record];
    }

    setSelectedRowKeys(cloneSelectedRowKeys);
    setSelectedRows(cloneSelectedRows);
  });

  const rowSelection: TableRowSelection<DataType> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const columns: TableProps<DataType>['columns'] = [
    {
      title: 'CRM Field Name',
      dataIndex: 'zohoLabel',
      width: 160,
      key: 'zohoLabel',
    },
    {
      title: 'Existing Information',
      dataIndex: 'zohoValue',
      width: 160,
      key: 'zohoValue',
    },
    {
      title: 'Enriched Information',
      dataIndex: 'enrichValue',
      width: 160,
      key: 'enrichValue',
    },
  ];

  return (
    <Modal
      title='Data Enrichment'
      open={open}
      onCancel={() => {
        handleCancel();
        onCancel?.();
      }}
      onOk={() => handleUpdateContact()}
      width={1000}
      maskClosable={false}
      okText={updating ? 'Updating' : 'Update'}
      cancelText='Cancel'
      okButtonProps={{ disabled: selectedRowKeys?.length === 0, loading: updating }}
    >
      {loading ? (
        <Flex
          justify='center'
          align='center'
          vertical
          style={{
            height: 200,
            width: '100%',
          }}
        >
          <Spin />
          <Typography.Text
            className='ant-spin-text'
            style={{
              fontSize: 14,
              textShadow: '0 1px 2px #fff',
              color: '#1677ff',
              marginTop: 14,
            }}
          >
            We are retrieving information from multiple sources. Please wait.
          </Typography.Text>
        </Flex>
      ) : data?.error ? (
        <Flex justify='center' align='center'>
          <Alert
            message={
              <span>
                Error occurred: {data.error}. You can click{' '}
                <Button onClick={() => enrichRun()}>Retry</Button> to try again.
              </span>
            }
            type='error'
          />
        </Flex>
      ) : dataSource.length > 0 ? (
        <Table
          loading={loading}
          size='small'
          rowKey='zohoKey'
          dataSource={dataSource}
          columns={columns}
          rowSelection={rowSelection}
          pagination={false}
          onRow={(record) => {
            return {
              onClick: () => {
                onRowClick(record);
              },
            };
          }}
        />
      ) : (
        <Flex justify='center' align='center'>
          <Empty description='No data to enrich' />
        </Flex>
      )}

      {contextHolder}
    </Modal>
  );
};

export default EnrichDataModal;
