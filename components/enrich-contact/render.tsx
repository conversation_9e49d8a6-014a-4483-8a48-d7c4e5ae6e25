import type { ContentScriptContext } from '#imports';
import { createAutoMountShadowRootUI } from '@/utils';
import ReactDOM from 'react-dom/client';
import EnrichContact from './index.tsx';

export const renderEnrichContact = async (ctx: ContentScriptContext) => {
  await createAutoMountShadowRootUI(ctx, {
    name: 'inhand-enrich-contact-button-host',
    position: 'inline',
    anchor: 'crm-detailview-actions[module="Contacts"]',
    pathMatches: ['/crm/*/tab/Contacts/*'],
    append: 'first',
    inheritStyles: true,
    isolateEvents: true,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.display = 'inline-block';
        shadowHost.style.zIndex = '99'; // 确保按钮在最上层
        shadowHost.style.marginRight = '10px'; // 调整位置
        shadowHost.classList.add('inhand-enrich-contact-button-host');
      }

      if (container) {
        container.style.display = 'inline-block';
        // 新增：独立 createRoot 渲染
        const reactContainer = document.createElement('div');
        reactContainer.id = 'inhand-enrich-contact-button-host-react-container';
        container.appendChild(reactContainer);
        const root = ReactDOM.createRoot(reactContainer);
        root.render(<EnrichContact />);
      }
    },
  });
};
