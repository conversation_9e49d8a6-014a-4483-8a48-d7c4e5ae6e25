import { getCurrentUser } from '@/utils';
import request from '@/utils/request.ts';
import { zohoRequest } from '@/utils/zoho-request.ts';

export const getContactInfo = async (contactId: string) => {
  const resp = await zohoRequest(`/crm/v2.2/Contacts/${contactId}`, {
    method: 'get',
  });
  return resp?.data?.[0];
};

export const enrichContact = async (contactId: string) => {
  const userInfo = await getCurrentUser();
  const statusData = await request(`/api/sales-agent/contact/${contactId}/enrich`, {
    method: 'get',
    params: {
      username: userInfo?.username,
      email: userInfo?.email,
    },
  });
  let zohoContactInfo;
  if (statusData?.status === 'success') {
    zohoContactInfo = await getContactInfo(contactId);
  }
  return {
    ...statusData,
    zohoContactInfo,
  };
};

export const updateContactInfo = async (contactId: string, contact: Record<string, any>) => {
  const submitData = {
    data: [
      {
        id: contactId,
        ...contact,
      },
    ],
    skip_mandatory: false,
  };

  return await zohoRequest(`/crm/v2.2/Contacts/${contactId}`, {
    method: 'PUT',
    data: submitData,
  });
};
