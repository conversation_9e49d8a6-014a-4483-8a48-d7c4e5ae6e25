import type { ContentScriptContext } from '#imports';
import { createAutoMountShadowRootUI } from '@/utils';
import ReactDOM from 'react-dom/client';
import SmartCreateContact from './index';

// maintain a global state to track if the UI is mounted
export const renderSmartCreateContactInAccountProfile = async (ctx: ContentScriptContext) => {
  await createAutoMountShadowRootUI(ctx, {
    name: 'inhand-smart-add-contact-in-account-profile',
    position: 'inline',
    pathMatches: ['/crm/*/tab/Accounts/*'],
    anchor: 'crm-related-list-view-header[related-list-label="Contacts"] crm-related-list-actions',
    append: 'first',
    inheritStyles: true,
    isolateEvents: false,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.display = 'inline-block';
        shadowHost.style.position = 'relative';
        shadowHost.style.verticalAlign = 'middle';
        shadowHost.style.margin = '4px 8px 4px 0';
        shadowHost.style.textAlign = 'left';
        shadowHost.classList.add('inhand-smart-add-contact-in-account-profile');
      }

      if (container) {
        container.style.display = 'inline-block';
        container.style.position = 'relative';
        container.style.verticalAlign = 'middle';
        // 新增：独立 createRoot 渲染
        const reactContainer = document.createElement('div');
        reactContainer.id = 'inhand-smart-add-contact-in-account-profile-ui-react-container';
        container.appendChild(reactContainer);
        const root = ReactDOM.createRoot(reactContainer);
        root.render(
          <SmartCreateContact
            targetLocation='account-profile'
            buttonProps={{
              size: 'small',
              type: 'default',
              style: {
                display: 'inline-flex',
                alignItems: 'center',
                gap: '8px',
                verticalAlign: 'middle',
                height: '28px',
                lineHeight: '28px',
              },
            }}
          />,
        );
      }
    },
  });
};

export const renderSmartCreateContactsInContactList = async (ctx: ContentScriptContext) => {
  await createAutoMountShadowRootUI(ctx, {
    name: 'inhand-smart-add-contact-in-contacts-list',
    position: 'inline',
    pathMatches: ['/crm/*/tab/Contacts/custom-view/*/list'],
    anchor: '#table_row_1 .customPluswithImpotBtnCon',
    append: 'after',
    inheritStyles: true,
    isolateEvents: false,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (shadowHost) {
        shadowHost.style.display = 'inline-block';
        shadowHost.style.zIndex = '99'; // 确保按钮在最上层
        shadowHost.style.marginRight = '10px'; // 调整位置
        shadowHost.style.verticalAlign = 'middle';
        shadowHost.classList.add('inhand-smart-add-contact-in-contacts-list');
      }

      if (container) {
        container.style.display = 'inline-block';
        container.style.position = 'relative';
        container.style.verticalAlign = 'middle';
        // 新增：独立 createRoot 渲染
        const reactContainer = document.createElement('div');
        reactContainer.id = 'inhand-smart-add-contact-in-contacts-list-ui-react-container';
        container.appendChild(reactContainer);
        const root = ReactDOM.createRoot(reactContainer);
        root.render(<SmartCreateContact targetLocation='contacts-list' />);
      }
    },
  });
};
