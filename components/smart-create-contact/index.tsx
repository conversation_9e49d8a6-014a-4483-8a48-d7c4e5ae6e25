import type { StreamMessage } from '@/components/detect-contact-popover/data';

import MarkdownTypingPreview from '@/components/common/markdown-typing-preview';
import AccountAutoSearch from '@/components/smart-create-contact/account-auto-search';
import { getAccountId, getAccountName, getCurrentUser } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request';
import { getZohoHeaderRequest } from '@/utils/zoho-request.ts';
import { Sender } from '@ant-design/x';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useRequest } from 'ahooks';
import type { ButtonProps } from 'antd';
import { Alert, Button, Flex, Form, Modal, Space, Spin, Typography } from 'antd';
import { capitalize } from 'es-toolkit/string';
import React from 'react';
import MagicSVG from '~/assets/magic-colorful.svg';

type ErrorType = 'not_found' | 'exists' | 'unknown_error' | 'network' | 'error';

interface SmartCreateContactInterface {
  targetLocation?: 'account-profile' | 'contacts-list';
  buttonProps?: ButtonProps;
}

const SmartCreateContact: React.FC<SmartCreateContactInterface> = ({
  targetLocation,
  buttonProps,
}) => {
  const isContactsList = targetLocation === 'contacts-list';
  const accountName = getAccountName();

  const [open, setOpen] = useState<boolean>(false);

  const [form] = Form.useForm();
  const [prompt, setPrompt] = useState<string | undefined>();
  const [selectedAccount, setSelectedAccount] = useState<string | undefined>();

  const [loadingMessageStream, setLoadingMessageStream] = useState<StreamMessage>();
  const [error, setError] = useState<
    | {
        type: ErrorType;
        message: string;
        data?: {
          name?: string;
          account_id?: string;
        };
      }
    | undefined
  >();

  const ctrlRef = useRef<AbortController | null>(null);

  const {
    loading,
    run: handleSubmit,
    cancel: cancelSubmit,
  } = useRequest(
    async (userInput) => {
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }
      setLoadingMessageStream(undefined);

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;

      const userInfo = await getCurrentUser();
      const request_headers = await getZohoHeaderRequest();
      const submitData = {
        user_input: userInput,
        user_info: userInfo,
        account_id: isContactsList ? selectedAccount : getAccountId(),
        request_headers: request_headers,
      };

      const streamURL = `${BASE_URL}/api/sales-agent/contact/smart-add`;
      await fetchEventSource(streamURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': X_API_KEY,
        },
        body: JSON.stringify(submitData),
        signal: ctrl.signal,
        openWhenHidden: true,
        onopen: async () => {
          setLoadingMessageStream({
            type: 'thinking',
            content: 'Start analysis',
            timestamp: new Date().toDateString(),
          });
        },
        onmessage: async (event) => {
          const data: StreamMessage = JSON.parse(event.data);
          setLoadingMessageStream(data);

          if (data.type === 'error') {
            const existData = data?.data;
            if (existData) {
              setError({
                type: 'exists',
                message: data?.content ?? '',
                data: data?.data,
              });
            } else {
              setError({
                type: 'error',
                message: data.content ?? 'Unknown error',
                data: data?.data,
              });
            }
          }
          if (data.type === 'finish' && data?.data) {
            window.location.href = `https://crm.zoho.com/crm/org663291548/tab/Contacts/${data?.data?.id}`;
            setPrompt(undefined);
            setOpen(false);
          }
        },
        onerror: (event) => {
          console.error('[inhand] SSE connection error', event);
          setError({
            type: 'network',
            message: 'SSE connection error',
          });
        },
        onclose: async () => {
          console.info('[inhand] SSE connection closed');
        },
      });
    },
    {
      manual: true,
      refreshDeps: [selectedAccount, isContactsList],
    },
  );

  return (
    <>
      <Button
        icon={
          <img src={MagicSVG} alt='magic' width={12} style={{ color: '#fff', marginTop: '-3px' }} />
        }
        {...buttonProps}
        style={{
          ...buttonProps?.style,
          display: 'inline-flex',
          alignItems: 'center',
          gap: '8px',
        }}
        onClick={() => setOpen(true)}
      >
        Smart Create
      </Button>
      {open && (
        <Modal
          title='Smart Create Contact'
          open={open}
          onCancel={() => {
            setOpen(false);
            setError(undefined);
            setLoadingMessageStream(undefined);
            setPrompt(undefined);
            setSelectedAccount(undefined);
          }}
          footer={false}
          width={620}
          styles={{
            body: {
              maxHeight: 800,
              overflowY: 'auto',
              minHeight: 120,
              display: 'grid',
              alignItems: 'center',
            },
          }}
          maskClosable={false}
        >
          <Space direction='vertical' size='middle' style={{ display: 'flex' }}>
            {loading && (
              <Flex
                style={{ minHeight: 100, width: '100%' }}
                justify='center'
                align='center'
                vertical
              >
                <Spin spinning={loading} size='large' />
                <Typography.Text type='secondary'>
                  <MarkdownTypingPreview
                    markdownText={loadingMessageStream?.content ?? 'Loading'}
                  />
                </Typography.Text>
              </Flex>
            )}
            {error && (
              <Alert
                message={
                  error.type === 'exists' ? (
                    <span>
                      This contact ({error?.data?.name ?? ''}) already exists; you can click the{' '}
                      <Typography.Link
                        href={`https://crm.zoho.com/crm/org663291548/tab/Contacts/${error?.data?.id}`}
                        target='_blank'
                      >
                        View in CRM
                      </Typography.Link>{' '}
                      to view it.
                    </span>
                  ) : (
                    capitalize(error?.message)
                  )
                }
                type='error'
              />
            )}
            <div style={{ marginBottom: 16, opacity: 0.6 }}>
              <Typography.Text style={{ fontSize: 11 }}>
                {isContactsList
                  ? 'You first need to select the target account where you want to create the contact, then enter the contact’s name or any identifying information you know. I will automatically complete the contact’s full details and create it within the account you selected.'
                  : `Simply enter the contact’s name or any identifying information you know, and I will automatically complete the contact’s full details and create it in ${accountName}.`}
              </Typography.Text>
            </div>
            {isContactsList && (
              <Form form={form}>
                <Form.Item
                  label='Account Name'
                  name='selectedAccount'
                  required
                  rules={[{ required: true, message: 'Please select an account' }]}
                >
                  <AccountAutoSearch
                    onChange={(account) => setSelectedAccount(account)}
                    disabled={loading}
                    placeholder={'Place select an account'}
                  />
                </Form.Item>
              </Form>
            )}
            <Sender
              loading={loading}
              value={prompt}
              onChange={(v) => {
                setLoadingMessageStream(undefined);
                setError(undefined);
                setPrompt(v);
              }}
              onSubmit={async () => {
                if (isContactsList) {
                  await form.validateFields();
                }
                if (prompt) {
                  setLoadingMessageStream(undefined);
                  setError(undefined);
                  handleSubmit(prompt);
                }
              }}
              onCancel={() => {
                ctrlRef.current?.abort();
                cancelSubmit();
              }}
              readOnly={loading}
              placeholder='Please enter as much information about the contact as you can: name, title, department, etc.'
              autoSize={{
                minRows: 2,
                maxRows: 2,
              }}
            />
            <Typography.Text style={{ marginTop: 16, opacity: 0.8 }}>
              <Typography.Text style={{ fontSize: 11 }}>Please note:</Typography.Text>
              <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                There is no need to add prefixes such as “help me create” or “please help me.”
              </Typography.Text>
            </Typography.Text>
            <Space direction='vertical' size={4} style={{ marginTop: 8, opacity: 0.6 }}>
              <Typography.Text style={{ fontSize: 11 }}>Examples:</Typography.Text>
              <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                • Michael Jackson
              </Typography.Text>
              <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                • Michael Jackson, Marketing
              </Typography.Text>
            </Space>
          </Space>
        </Modal>
      )}
    </>
  );
};

export default SmartCreateContact;
