import type { ProSelectProps } from '@/components/common/ProSelect';
import ProSelect from '@/components/common/ProSelect';
import React from 'react';
import { fetchAccounts } from './services.ts';

interface AccountAutoSearchProps extends ProSelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

const AccountAutoSearch: React.FC<AccountAutoSearchProps> = (props) => {
  return (
    <ProSelect
      {...props}
      request={fetchAccounts}
      searchKeyword='name'
      fieldNames={{
        label: 'Account_Name',
        value: 'id',
      }}
    />
  );
};

export default AccountAutoSearch;
