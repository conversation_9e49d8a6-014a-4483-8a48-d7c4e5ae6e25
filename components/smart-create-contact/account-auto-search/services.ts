import zohoRequest from '@/utils/zoho-request.ts';
import { isEmpty } from 'es-toolkit/compat';

export const zohoAccountList = async (
  params: FetchAccountsParams & {
    per_page: number;
  },
) => {
  return await zohoRequest('/crm/v2.2/Accounts/bulk', {
    method: 'POST',
    params,
  });
};

export const zohoSearch = async (
  params: FetchAccountsParams & {
    per_page: number;
  },
) => {
  return await zohoRequest('crm/v2.2/Accounts/search', {
    params: {
      ...params,
      criteria: `(Account_Name:starts_with:${params.name})`,
    },
  });
};

export type FetchAccountsParams = {
  page?: number;
  name?: string;
  [key: string]: unknown;
};

export const fetchAccounts = async (params: FetchAccountsParams) => {
  const { page, name } = params;
  const queryParams = {
    page: page ?? 1,
    per_page: 11,
    approved: 'both',
    approval_state:
      'approved%2Capproval_process_pending%2Capproval_process_rejected%2Capproval_process_recalled',
  };
  if (isEmpty(name)) {
    const { data, info } = await zohoAccountList(queryParams);
    const { count, per_page } = info;
    return {
      result: data,
      page: info?.page ?? queryParams.page,
      noMore: count && per_page && count < per_page,
    };
  }

  const { data, info } = await zohoSearch({
    ...queryParams,
    name,
  });

  const { count, per_page } = info;
  // 如果 count, per_page 都有值，并且 count 小于 per_page 时， 则说明没有更多的内容了
  return {
    result: data,
    page: info?.page ?? queryParams.page,
    noMore: count && per_page && count < per_page,
  };
};
