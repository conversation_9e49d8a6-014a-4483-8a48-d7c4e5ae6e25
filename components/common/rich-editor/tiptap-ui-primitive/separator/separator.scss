.tiptap-separator {
  --tt-link-border-color: var(--tt-gray-light-a-200);

  .dark & {
    --tt-link-border-color: var(--tt-gray-dark-a-200);
  }
}

.tiptap-separator {
  flex-shrink: 0;
  background-color: var(--tt-link-border-color);

  &[data-orientation="horizontal"] {
    height: 1px;
    width: 100%;
    margin: 0.5rem 0;
  }

  &[data-orientation="vertical"] {
    height: 1.5rem;
    width: 1px;
  }
}
