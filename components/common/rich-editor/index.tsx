import { SimpleEditor } from '@/components/common/rich-editor/editor/simple-editor.tsx';
import customCss from './index.css?raw'
import React, {useEffect} from 'react';

const RichEditor: React.FC = () => {
  useEffect(() => {
    const style = document.createElement('style');
    style.id = 'rich-editor-custom-styles';
    style.innerHTML = customCss;
    document.head.appendChild(style);

    return () => {
      const existingStyle = document.getElementById(style.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  return <SimpleEditor />;
};

export default RichEditor;
