import { SimpleEditor } from './tiptap-templates/simple/simple-editor';
import customCss from './index.css?raw';

// Core styles
import keyframeAnimationsCss from './styles/_keyframe-animations.css?raw';
import variablesCss from './styles/_variables.css?raw';

// Template styles
import simpleEditorCss from './tiptap-templates/simple/simple-editor.css?raw';

// UI Primitive styles
import badgeCss from './tiptap-ui-primitive/badge/badge.css?raw';
import badgeColorsCss from './tiptap-ui-primitive/badge/badge-colors.css?raw';
import badgeGroupCss from './tiptap-ui-primitive/badge/badge-group.css?raw';
import buttonCss from './tiptap-ui-primitive/button/button.css?raw';
import buttonColorsCss from './tiptap-ui-primitive/button/button-colors.css?raw';
import buttonGroupCss from './tiptap-ui-primitive/button/button-group.css?raw';
import cardCss from './tiptap-ui-primitive/card/card.css?raw';
import dropdownMenuCss from './tiptap-ui-primitive/dropdown-menu/dropdown-menu.css?raw';
import inputCss from './tiptap-ui-primitive/input/input.css?raw';
import popoverCss from './tiptap-ui-primitive/popover/popover.css?raw';
import separatorCss from './tiptap-ui-primitive/separator/separator.css?raw';
import toolbarCss from './tiptap-ui-primitive/toolbar/toolbar.css?raw';
import tooltipCss from './tiptap-ui-primitive/tooltip/tooltip.css?raw';

// Node styles
import blockquoteNodeCss from './tiptap-node/blockquote-node/blockquote-node.css?raw';
import codeBlockNodeCss from './tiptap-node/code-block-node/code-block-node.css?raw';
import headingNodeCss from './tiptap-node/heading-node/heading-node.css?raw';
import horizontalRuleNodeCss from './tiptap-node/horizontal-rule-node/horizontal-rule-node.css?raw';
import imageNodeCss from './tiptap-node/image-node/image-node.css?raw';
import imageUploadNodeCss from './tiptap-node/image-upload-node/image-upload-node.css?raw';
import listNodeCss from './tiptap-node/list-node/list-node.css?raw';
import paragraphNodeCss from './tiptap-node/paragraph-node/paragraph-node.css?raw';

// UI Component styles
import colorHighlightButtonCss from './tiptap-ui/color-highlight-button/color-highlight-button.css?raw';

import React, {useEffect} from 'react';

const RichEditor: React.FC = () => {
  useEffect(() => {
    // Combine all CSS styles
    const allStyles = [
      customCss,
      // Core styles
      keyframeAnimationsCss,
      variablesCss,
      // Template styles
      simpleEditorCss,
      // UI Primitive styles
      badgeCss,
      badgeColorsCss,
      badgeGroupCss,
      buttonCss,
      buttonColorsCss,
      buttonGroupCss,
      cardCss,
      dropdownMenuCss,
      inputCss,
      popoverCss,
      separatorCss,
      toolbarCss,
      tooltipCss,
      // Node styles
      blockquoteNodeCss,
      codeBlockNodeCss,
      headingNodeCss,
      horizontalRuleNodeCss,
      imageNodeCss,
      imageUploadNodeCss,
      listNodeCss,
      paragraphNodeCss,
      // UI Component styles
      colorHighlightButtonCss,
    ].join('\n');

    const style = document.createElement('style');
    style.id = 'rich-editor-custom-styles';
    style.innerHTML = allStyles;
    document.head.appendChild(style);

    return () => {
      const existingStyle = document.getElementById(style.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  return <SimpleEditor />;
};

export default RichEditor;
