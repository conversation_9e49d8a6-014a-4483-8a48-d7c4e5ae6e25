import { SimpleEditor } from '@/components/common/rich-editor/editor/simple-editor.tsx';
import customCss from './index.css?raw'
import React, {useEffect} from 'react';

const RichEditor: React.FC = () => {
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = customCss;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return <SimpleEditor />;
};

export default RichEditor;
