/* =====================
   CSS VARIABLES
   ===================== */
:root {
  /******************
  Basics
  ******************/

  overflow-wrap: break-word;
  text-size-adjust: none;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /******************
  Colors variables
  ******************/

  /* Gray alpha (light mode) */
  --tt-gray-light-a-50: rgba(56, 56, 56, 0.04);
  --tt-gray-light-a-100: rgba(15, 22, 36, 0.05);
  --tt-gray-light-a-200: rgba(37, 39, 45, 0.1);
  --tt-gray-light-a-300: rgba(47, 50, 55, 0.2);
  --tt-gray-light-a-400: rgba(40, 44, 51, 0.42);
  --tt-gray-light-a-500: rgba(52, 55, 60, 0.64);
  --tt-gray-light-a-600: rgba(36, 39, 46, 0.78);
  --tt-gray-light-a-700: rgba(35, 37, 42, 0.87);
  --tt-gray-light-a-800: rgba(30, 32, 36, 0.95);
  --tt-gray-light-a-900: rgba(29, 30, 32, 0.98);

  /* Gray (light mode) */
  --tt-gray-light-50: rgba(250, 250, 250, 1);
  --tt-gray-light-100: rgba(244, 244, 245, 1);
  --tt-gray-light-200: rgba(234, 234, 235, 1);
  --tt-gray-light-300: rgba(213, 214, 215, 1);
  --tt-gray-light-400: rgba(166, 167, 171, 1);
  --tt-gray-light-500: rgba(125, 127, 130, 1);
  --tt-gray-light-600: rgba(83, 86, 90, 1);
  --tt-gray-light-700: rgba(64, 65, 69, 1);
  --tt-gray-light-800: rgba(44, 45, 48, 1);
  --tt-gray-light-900: rgba(34, 35, 37, 1);


  /* Brand colors */
  --tt-brand-color-50: rgba(239, 238, 255, 1);
  --tt-brand-color-100: rgba(222, 219, 255, 1);
  --tt-brand-color-200: rgba(195, 189, 255, 1);
  --tt-brand-color-300: rgba(157, 138, 255, 1);
  --tt-brand-color-400: rgba(122, 82, 255, 1);
  --tt-brand-color-500: rgba(98, 41, 255, 1);
  --tt-brand-color-600: rgba(84, 0, 229, 1);
  --tt-brand-color-700: rgba(75, 0, 204, 1);
  --tt-brand-color-800: rgba(56, 0, 153, 1);
  --tt-brand-color-900: rgba(43, 25, 102, 1);
  --tt-brand-color-950: hsla(257deg, 100%, 9%, 1);

  /* Green */
  --tt-color-green-inc-5: hsla(129deg, 100%, 97%, 1);
  --tt-color-green-inc-4: hsla(129deg, 100%, 92%, 1);
  --tt-color-green-inc-3: hsla(131deg, 100%, 86%, 1);
  --tt-color-green-inc-2: hsla(133deg, 98%, 78%, 1);
  --tt-color-green-inc-1: hsla(137deg, 99%, 70%, 1);
  --tt-color-green-base: hsla(147deg, 99%, 50%, 1);
  --tt-color-green-dec-1: hsla(147deg, 97%, 41%, 1);
  --tt-color-green-dec-2: hsla(146deg, 98%, 32%, 1);
  --tt-color-green-dec-3: hsla(146deg, 100%, 24%, 1);
  --tt-color-green-dec-4: hsla(144deg, 100%, 16%, 1);
  --tt-color-green-dec-5: hsla(140deg, 100%, 9%, 1);

  /* Yellow */
  --tt-color-yellow-inc-5: hsla(50deg, 100%, 97%, 1);
  --tt-color-yellow-inc-4: hsla(50deg, 100%, 91%, 1);
  --tt-color-yellow-inc-3: hsla(50deg, 100%, 84%, 1);
  --tt-color-yellow-inc-2: hsla(50deg, 100%, 77%, 1);
  --tt-color-yellow-inc-1: hsla(50deg, 100%, 68%, 1);
  --tt-color-yellow-base: hsla(52deg, 100%, 50%, 1);
  --tt-color-yellow-dec-1: hsla(52deg, 100%, 41%, 1);
  --tt-color-yellow-dec-2: hsla(52deg, 100%, 32%, 1);
  --tt-color-yellow-dec-3: hsla(52deg, 100%, 24%, 1);
  --tt-color-yellow-dec-4: hsla(51deg, 100%, 16%, 1);
  --tt-color-yellow-dec-5: hsla(50deg, 100%, 9%, 1);

  /* Red */
  --tt-color-red-inc-5: hsla(11deg, 100%, 96%, 1);
  --tt-color-red-inc-4: hsla(11deg, 100%, 88%, 1);
  --tt-color-red-inc-3: hsla(10deg, 100%, 80%, 1);
  --tt-color-red-inc-2: hsla(9deg, 100%, 73%, 1);
  --tt-color-red-inc-1: hsla(7deg, 100%, 64%, 1);
  --tt-color-red-base: hsla(7deg, 100%, 54%, 1);
  --tt-color-red-dec-1: hsla(7deg, 100%, 41%, 1);
  --tt-color-red-dec-2: hsla(5deg, 100%, 32%, 1);
  --tt-color-red-dec-3: hsla(4deg, 100%, 24%, 1);
  --tt-color-red-dec-4: hsla(3deg, 100%, 16%, 1);
  --tt-color-red-dec-5: hsla(1deg, 100%, 9%, 1);

  /* Basic colors */
  --white: rgba(255, 255, 255, 1);
  --black: rgba(14, 14, 17, 1);
  --transparent: rgba(255, 255, 255, 0);

  /******************
  Shadow variables
  ******************/

  /* Shadows Light */
  --tt-shadow-elevated-md: 0px 16px 48px 0px rgba(17, 24, 39, 0.04), 0px 12px 24px 0px rgba(17, 24, 39, 0.04),
  0px 6px 8px 0px rgba(17, 24, 39, 0.02), 0px 2px 3px 0px rgba(17, 24, 39, 0.02);

  /**************************************************
       Radius variables
    **************************************************/

  --tt-radius-xxs: 0.125rem; /* 2px */
  --tt-radius-xs: 0.25rem; /* 4px */
  --tt-radius-sm: 0.375rem; /* 6px */
  --tt-radius-md: 0.5rem; /* 8px */
  --tt-radius-lg: 0.75rem; /* 12px */
  --tt-radius-xl: 1rem; /* 16px */

  /**************************************************
       Transition variables
    **************************************************/

  --tt-transition-duration-short: 0.1s;
  --tt-transition-duration-default: 0.2s;
  --tt-transition-duration-long: 0.64s;
  --tt-transition-easing-default: cubic-bezier(0.46, 0.03, 0.52, 0.96);
  --tt-transition-easing-cubic: cubic-bezier(0.65, 0.05, 0.36, 1);
  --tt-transition-easing-quart: cubic-bezier(0.77, 0, 0.18, 1);
  --tt-transition-easing-circ: cubic-bezier(0.79, 0.14, 0.15, 0.86);
  --tt-transition-easing-back: cubic-bezier(0.68, -0.55, 0.27, 1.55);

  /******************
  Contrast variables
  ******************/

  --tt-accent-contrast: 8%;
  --tt-destructive-contrast: 8%;
  --tt-foreground-contrast: 8%;
}

:root,
:root *,
:root ::before,
:root ::after {
  box-sizing: border-box;
  transition: none var(--tt-transition-duration-default) var(--tt-transition-easing-default);
}

:root {
  /**************************************************
      Global colors
  **************************************************/

  /* Global colors - Light mode */
  --tt-bg-color: var(--white);
  --tt-border-color: var(--tt-gray-light-a-200);
  --tt-border-color-tint: var(--tt-gray-light-a-100);
  --tt-sidebar-bg-color: var(--tt-gray-light-100);
  --tt-scrollbar-color: var(--tt-gray-light-a-200);
  --tt-cursor-color: var(--tt-brand-color-500);
  --tt-selection-color: rgba(157, 138, 255, 0.2);
  --tt-card-bg-color: var(--white);
  --tt-card-border-color: var(--tt-gray-light-a-100);
}


/* Text colors */
:root {
  --tt-color-text-gray: hsl(45deg, 2%, 46%);
  --tt-color-text-brown: hsl(19deg, 31%, 47%);
  --tt-color-text-orange: hsl(30deg, 89%, 45%);
  --tt-color-text-yellow: hsl(38deg, 62%, 49%);
  --tt-color-text-green: hsl(148deg, 32%, 39%);
  --tt-color-text-blue: hsl(202deg, 54%, 43%);
  --tt-color-text-purple: hsl(274deg, 32%, 54%);
  --tt-color-text-pink: hsl(328deg, 49%, 53%);
  --tt-color-text-red: hsl(2deg, 62%, 55%);
  --tt-color-text-gray-contrast: hsla(39deg, 26%, 26%, 0.15);
  --tt-color-text-brown-contrast: hsla(18deg, 43%, 69%, 0.35);
  --tt-color-text-orange-contrast: hsla(24deg, 73%, 55%, 0.27);
  --tt-color-text-yellow-contrast: hsla(44deg, 82%, 59%, 0.39);
  --tt-color-text-green-contrast: hsla(126deg, 29%, 60%, 0.27);
  --tt-color-text-blue-contrast: hsla(202deg, 54%, 59%, 0.27);
  --tt-color-text-purple-contrast: hsla(274deg, 37%, 64%, 0.27);
  --tt-color-text-pink-contrast: hsla(331deg, 60%, 71%, 0.27);
  --tt-color-text-red-contrast: hsla(8deg, 79%, 79%, 0.4);
}

.dark {
  --tt-color-text-gray: hsl(0deg, 0%, 61%);
  --tt-color-text-brown: hsl(18deg, 35%, 58%);
  --tt-color-text-orange: hsl(25deg, 53%, 53%);
  --tt-color-text-yellow: hsl(36deg, 54%, 55%);
  --tt-color-text-green: hsl(145deg, 32%, 47%);
  --tt-color-text-blue: hsl(202deg, 64%, 52%);
  --tt-color-text-purple: hsl(270deg, 55%, 62%);
  --tt-color-text-pink: hsl(329deg, 57%, 58%);
  --tt-color-text-red: hsl(1deg, 69%, 60%);
  --tt-color-text-gray-contrast: hsla(0deg, 0%, 100%, 0.09);
  --tt-color-text-brown-contrast: hsla(17deg, 45%, 50%, 0.25);
  --tt-color-text-orange-contrast: hsla(27deg, 82%, 53%, 0.2);
  --tt-color-text-yellow-contrast: hsla(35deg, 49%, 47%, 0.2);
  --tt-color-text-green-contrast: hsla(151deg, 55%, 39%, 0.2);
  --tt-color-text-blue-contrast: hsla(202deg, 54%, 43%, 0.2);
  --tt-color-text-purple-contrast: hsla(271deg, 56%, 60%, 0.18);
  --tt-color-text-pink-contrast: hsla(331deg, 67%, 58%, 0.22);
  --tt-color-text-red-contrast: hsla(0deg, 67%, 60%, 0.25);
}

/* Highlight colors */
:root {
  --tt-color-highlight-yellow: #fef9c3;
  --tt-color-highlight-green: #dcfce7;
  --tt-color-highlight-blue: #e0f2fe;
  --tt-color-highlight-purple: #f3e8ff;
  --tt-color-highlight-red: #ffe4e6;
  --tt-color-highlight-gray: rgb(248, 248, 247);
  --tt-color-highlight-brown: rgb(244, 238, 238);
  --tt-color-highlight-orange: rgb(251, 236, 221);
  --tt-color-highlight-pink: rgb(252, 241, 246);
  --tt-color-highlight-yellow-contrast: #fbe604;
  --tt-color-highlight-green-contrast: #c7fad8;
  --tt-color-highlight-blue-contrast: #ceeafd;
  --tt-color-highlight-purple-contrast: #e4ccff;
  --tt-color-highlight-red-contrast: #ffccd0;
  --tt-color-highlight-gray-contrast: rgba(84, 72, 49, 0.15);
  --tt-color-highlight-brown-contrast: rgba(210, 162, 141, 0.35);
  --tt-color-highlight-orange-contrast: rgba(224, 124, 57, 0.27);
  --tt-color-highlight-pink-contrast: rgba(225, 136, 179, 0.27);
}


/* =====================
   KEYFRAME ANIMATIONS
   ===================== */
@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes zoom-in {
  from {
    transform: scale(0.95);
  }

  to {
    transform: scale(1);
  }
}

@keyframes zoom-out {
  from {
    transform: scale(1);
  }

  to {
    transform: scale(0.95);
  }
}

@keyframes zoom {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-from-top {
  from {
    transform: translateY(-0.5rem);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes slide-from-right {
  from {
    transform: translateX(0.5rem);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slide-from-left {
  from {
    transform: translateX(-0.5rem);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slide-from-bottom {
  from {
    transform: translateY(0.5rem);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

body {
  --tt-toolbar-height: 44px;
  --tt-theme-text: var(--tt-gray-light-900);

  font-family: Inter, sans-serif;
  color: var(--tt-theme-text);
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  padding: 0;
  overscroll-behavior-y: none;
}

.dark body {
  --tt-theme-text: var(--tt-gray-dark-900);
}

html,
body {
  overscroll-behavior-x: none;
}

html,
body,
#root,
#app {
  height: 100%;
  background-color: var(--tt-bg-color);
}

::-webkit-scrollbar {
  width: 0.25rem;
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--tt-scrollbar-color) transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--tt-scrollbar-color);
  border-radius: 9999px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* =====================
   SIMPLE EDITOR LAYOUT
   ===================== */
.tiptap.ProseMirror {
  font-family: 'DM Sans', sans-serif;
}

.simple-editor-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.simple-editor-content {
  max-width: 648px;
  width: 100%;
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.simple-editor-content .tiptap.ProseMirror.simple-editor {
  flex: 1;
  padding: 3rem 3rem 30vh;
}

@media screen and (width <= 480px) {
  .simple-editor-content .tiptap.ProseMirror.simple-editor {
    padding: 1rem 1.5rem 30vh;
  }
}

/* =====================
   TIPTAP CORE STYLES
   ===================== */
.tiptap.ProseMirror {
  --tt-collaboration-carets-label: var(--tt-gray-light-900);
  --link-text-color: var(--tt-brand-color-500);
  --thread-text: var(--tt-gray-light-900);
  --placeholder-color: var(--tt-gray-light-a-400);
  --thread-bg-color: var(--tt-color-yellow-inc-2);
  --tiptap-ai-insertion-color: var(--tt-brand-color-600);
  --blockquote-bg-color: var(--tt-gray-light-900);
  --tt-inline-code-bg-color: var(--tt-gray-light-a-100);
  --tt-inline-code-text-color: var(--tt-gray-light-a-700);
  --tt-inline-code-border-color: var(--tt-gray-light-a-200);
  --tt-codeblock-bg: var(--tt-gray-light-a-50);
  --tt-codeblock-text: var(--tt-gray-light-a-800);
  --tt-codeblock-border: var(--tt-gray-light-a-200);
  --horizontal-rule-color: var(--tt-gray-light-a-200);
  --tt-checklist-bg-color: var(--tt-gray-light-a-100);
  --tt-checklist-bg-active-color: var(--tt-gray-light-a-900);
  --tt-checklist-border-color: var(--tt-gray-light-a-200);
  --tt-checklist-border-active-color: var(--tt-gray-light-a-900);
  --tt-checklist-check-icon-color: var(--white);
  --tt-checklist-text-active: var(--tt-gray-light-a-500);
}


/* Ensure each top-level node has relative positioning */
.tiptap.ProseMirror > * {
  position: relative;
}

.tiptap.ProseMirror {
  white-space: pre-wrap;
  outline: none;
  caret-color: var(--tt-cursor-color);
}

/* Paragraph spacing */
.tiptap.ProseMirror p:not(:first-child) {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: normal;
  margin-top: 20px;
}

/* Selection styles */
.tiptap.ProseMirror:not(.readonly, .ProseMirror-hideselection) ::selection {
  background-color: var(--tt-selection-color);
}

.tiptap.ProseMirror:not(.readonly, .ProseMirror-hideselection) .selection::selection {
  background: transparent;
}

.tiptap.ProseMirror .selection {
  display: inline;
  background-color: var(--tt-selection-color);
}

/* Selected node styles */
.tiptap.ProseMirror .ProseMirror-selectednode:not(img, pre, .react-renderer) {
  border-radius: var(--tt-radius-md);
  background-color: var(--tt-selection-color);
}

.tiptap.ProseMirror .ProseMirror-hideselection {
  caret-color: transparent;
}

/* Resize cursor */
.tiptap.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* =====================
   HEADINGS
   ===================== */
.tiptap.ProseMirror h1,
.tiptap.ProseMirror h2,
.tiptap.ProseMirror h3,
.tiptap.ProseMirror h4 {
  position: relative;
  color: inherit;
  font-style: inherit;
}

.tiptap.ProseMirror h1:first-child,
.tiptap.ProseMirror h2:first-child,
.tiptap.ProseMirror h3:first-child,
.tiptap.ProseMirror h4:first-child {
  margin-top: 0;
}

.tiptap.ProseMirror h1 {
  font-size: 1.5em;
  font-weight: 700;
  margin-top: 3em;
}

.tiptap.ProseMirror h2 {
  font-size: 1.25em;
  font-weight: 700;
  margin-top: 2.5em;
}

.tiptap.ProseMirror h3 {
  font-size: 1.125em;
  font-weight: 600;
  margin-top: 2em;
}

.tiptap.ProseMirror h4 {
  font-size: 1em;
  font-weight: 600;
  margin-top: 2em;
}

/* =====================
   BLOCKQUOTES
   ===================== */
.tiptap.ProseMirror blockquote {
  position: relative;
  padding-left: 1em;
  padding-top: 0.375em;
  padding-bottom: 0.375em;
  margin: 1.5rem 0;
}

.tiptap.ProseMirror blockquote p {
  margin-top: 0;
}

.tiptap.ProseMirror blockquote::before,
.tiptap.ProseMirror blockquote.is-empty::before {
  position: absolute;
  bottom: 0;
  left: 0;
  top: 0;
  height: 100%;
  width: 0.25em;
  background-color: var(--blockquote-bg-color);
  content: '';
  border-radius: 0;
}

/* =====================
   CODE FORMATTING
   ===================== */

/* Inline code */
.tiptap.ProseMirror code {
  background-color: var(--tt-inline-code-bg-color);
  color: var(--tt-inline-code-text-color);
  border: 1px solid var(--tt-inline-code-border-color);
  font-family: 'JetBrains Mono NL', monospace;
  font-size: 0.875em;
  line-height: 1.4;
  border-radius: 6px / 0.375rem;
  padding: 0.1em 0.2em;
}

/* Code blocks */
.tiptap.ProseMirror pre {
  background-color: var(--tt-codeblock-bg);
  color: var(--tt-codeblock-text);
  border: 1px solid var(--tt-codeblock-border);
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  padding: 1em;
  font-size: 1rem;
  border-radius: 6px / 0.375rem;
}

.tiptap.ProseMirror pre code {
  background-color: transparent;
  border: none;
  border-radius: 0;
  -webkit-text-fill-color: inherit;
  color: inherit;
}

/* =====================
   HORIZONTAL RULE
   ===================== */
.tiptap.ProseMirror hr {
  border: none;
  height: 1px;
  background-color: var(--horizontal-rule-color);
}

.tiptap.ProseMirror [data-type='horizontalRule'] {
  margin-top: 2.25em;
  margin-bottom: 2.25em;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

/* =====================
   IMAGES
   ===================== */
.tiptap.ProseMirror img {
  max-width: 100%;
  height: auto;
  display: block;
}

.tiptap.ProseMirror > img:not([data-type='emoji'] img) {
  margin: 2rem 0;
  outline: 0.125rem solid transparent;
  border-radius: var(--tt-radius-xs, 0.25rem);
}

.tiptap.ProseMirror img:not([data-type='emoji'] img).ProseMirror-selectednode {
  outline-color: var(--tt-brand-color-500);
}

/* Thread image handling */
.tiptap.ProseMirror .tiptap-thread:has(> img) {
  margin: 2rem 0;
}

.tiptap.ProseMirror .tiptap-thread:has(> img) img {
  outline: 0.125rem solid transparent;
  border-radius: var(--tt-radius-xs, 0.25rem);
}

.tiptap.ProseMirror .tiptap-thread img {
  margin: 0;
}

/* =====================
   LISTS
   ===================== */

/* Common list styles */
.tiptap.ProseMirror ol,
.tiptap.ProseMirror ul {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  padding-left: 1.5em;
}

.tiptap.ProseMirror ol:first-child,
.tiptap.ProseMirror ul:first-child {
  margin-top: 0;
}

.tiptap.ProseMirror ol:last-child,
.tiptap.ProseMirror ul:last-child {
  margin-bottom: 0;
}

.tiptap.ProseMirror ol ol,
.tiptap.ProseMirror ol ul,
.tiptap.ProseMirror ul ol,
.tiptap.ProseMirror ul ul {
  margin-top: 0;
  margin-bottom: 0;
}

.tiptap.ProseMirror li p {
  margin-top: 0;
  line-height: 1.6;
}

/* Ordered lists */
.tiptap.ProseMirror ol {
  list-style: decimal;
}

.tiptap.ProseMirror ol ol {
  list-style: lower-alpha;
}

.tiptap.ProseMirror ol ol ol {
  list-style: lower-roman;
}

/* Unordered lists */
.tiptap.ProseMirror ul:not([data-type='taskList']) {
  list-style: disc;
}

.tiptap.ProseMirror ul:not([data-type='taskList']) ul {
  list-style: circle;
}

.tiptap.ProseMirror ul:not([data-type='taskList']) ul ul {
  list-style: square;
}

/* Task lists */
.tiptap.ProseMirror ul[data-type='taskList'] {
  padding-left: 0.25em;
}

.tiptap.ProseMirror ul[data-type='taskList'] li {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.tiptap.ProseMirror ul[data-type='taskList'] li:not(:has(> p:first-child)) {
  list-style-type: none;
}

.tiptap.ProseMirror ul[data-type='taskList'] li[data-checked='true'] > div > p {
  opacity: 0.5;
  text-decoration: line-through;
}

.tiptap.ProseMirror ul[data-type='taskList'] li[data-checked='true'] > div > p span {
  text-decoration: line-through;
}

.tiptap.ProseMirror ul[data-type='taskList'] li label {
  position: relative;
  padding-top: 0.375rem;
  padding-right: 0.5rem;
}

.tiptap.ProseMirror ul[data-type='taskList'] li label input[type='checkbox'] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.tiptap.ProseMirror ul[data-type='taskList'] li label span {
  display: block;
  width: 1em;
  height: 1em;
  border: 1px solid var(--tt-checklist-border-color);
  border-radius: var(--tt-radius-xs, 0.25rem);
  position: relative;
  cursor: pointer;
  background-color: var(--tt-checklist-bg-color);
  transition: background-color 80ms ease-out,
  border-color 80ms ease-out;
}

.tiptap.ProseMirror ul[data-type='taskList'] li label span::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 0.75em;
  height: 0.75em;
  background-color: var(--tt-checklist-check-icon-color);
  opacity: 0;
  mask: url('data:image/svg+xml,%3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20fill%3D%22currentColor%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M21.4142%204.58579C22.1953%205.36683%2022.1953%206.63317%2021.4142%207.41421L10.4142%2018.4142C9.63317%2019.1953%208.36684%2019.1953%207.58579%2018.4142L2.58579%2013.4142C1.80474%2012.6332%201.80474%2011.3668%202.58579%2010.5858C3.36683%209.80474%204.63317%209.80474%205.41421%2010.5858L9%2014.1716L18.5858%204.58579C19.3668%203.80474%2020.6332%203.80474%2021.4142%204.58579Z%22%20fill%3D%22currentColor%22%2F%3E%3C%2Fsvg%3E') center/contain no-repeat;
}

.tiptap.ProseMirror ul[data-type='taskList'] li label input[type='checkbox']:checked + span {
  background: var(--tt-checklist-bg-active-color);
  border-color: var(--tt-checklist-border-active-color);
}

.tiptap.ProseMirror ul[data-type='taskList'] li label input[type='checkbox']:checked + span::before {
  opacity: 1;
}

.tiptap.ProseMirror ul[data-type='taskList'] li div {
  flex: 1 1 0%;
  min-width: 0;
}

/* =====================
   TEXT DECORATION
   ===================== */

/* Text decoration inheritance for spans */
.tiptap.ProseMirror a span {
  text-decoration: underline;
}

.tiptap.ProseMirror s span {
  text-decoration: line-through;
}

.tiptap.ProseMirror u span {
  text-decoration: underline;
}

.tiptap.ProseMirror .tiptap-ai-insertion {
  color: var(--tiptap-ai-insertion-color);
}

/* =====================
   LINKS
   ===================== */
.tiptap.ProseMirror a {
  color: var(--link-text-color);
  text-decoration: underline;
}

/* =====================
   EMOJI
   ===================== */
.tiptap.ProseMirror [data-type='emoji'] img {
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  cursor: text;
}

/* =====================
   MENTION
   ===================== */
.tiptap.ProseMirror [data-type='mention'] {
  display: inline-block;
  color: var(--tt-brand-color-500);
}

/* =====================
   PLACEHOLDER
   ===================== */
.is-empty:not(.with-slash)[data-placeholder]:has(> .ProseMirror-trailingBreak:only-child)::before {
  content: attr(data-placeholder);
}

.is-empty.with-slash[data-placeholder]:has(> .ProseMirror-trailingBreak:only-child)::before {
  content: "Write, type '/' for commands…";
  font-style: italic;
}

.is-empty[data-placeholder]:has(> .ProseMirror-trailingBreak:only-child)::before {
  pointer-events: none;
  height: 0;
  position: absolute;
  width: 100%;
  text-align: inherit;
  left: 0;
  right: 0;
}

.is-empty[data-placeholder]:has(> .ProseMirror-trailingBreak)::before {
  color: var(--placeholder-color);
}

/* =====================
   DROPCURSOR
   ===================== */
.prosemirror-dropcursor-block,
.prosemirror-dropcursor-inline {
  background: var(--tt-brand-color-400) !important;
  border-radius: 0.25rem;
  margin-left: -1px;
  margin-right: -1px;
  width: 100%;
  height: 0.188rem;
  cursor: grabbing;
}

/* =====================
   COLLABORATION
   ===================== */
.tiptap.ProseMirror .collaboration-carets__caret {
  border-right: 1px solid transparent;
  border-left: 1px solid transparent;
  pointer-events: none;
  margin-left: -1px;
  margin-right: -1px;
  position: relative;
  word-break: normal;
}

.tiptap.ProseMirror .collaboration-carets__label {
  color: var(--tt-collaboration-carets-label);
  border-radius: 0.25rem;
  border-bottom-left-radius: 0;
  font-size: 0.75rem;
  font-weight: 600;
  left: -1px;
  line-height: 1;
  padding: 0.125rem 0.375rem;
  position: absolute;
  top: -1.3em;
  user-select: none;
  white-space: nowrap;
}
