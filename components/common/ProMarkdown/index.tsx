import { Layout } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeSlug from 'rehype-slug';
import remarkGfm from 'remark-gfm';
import './index.css';

const { Sider, Content } = Layout;

// 提取标题信息的函数
const extractHeadings = (markdown: string) => {
  const headings: { text: string; id: string; level: number }[] = [];
  // 简单的正则表达式来匹配标题，但请注意，对于复杂的 Markdown 语法，这可能不够健壮
  const lines = markdown.split('\n');
  lines.forEach((line) => {
    if (line.startsWith('#')) {
      const match = line.match(/^(#+)\s(.+)/);
      if (match) {
        const level = match[1].length;
        const text = match[2];
        const id = text.toLowerCase().replace(/\s+/g, '-');
        headings.push({ text, id, level });
      }
    }
  });
  return headings;
};

interface ProMarkdownProps {
  content: string;
  className?: string;
  hiddenSider?: boolean;
  siderPosition?: 'left' | 'right';
  style?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
}

const ProMarkdown: React.FC<ProMarkdownProps> = ({
  content,
  siderPosition = 'right',
  hiddenSider = false,
  style,
  contentStyle,
}) => {
  const headings = extractHeadings(content);
  const isRightSide = siderPosition === 'right';
  const [activeHeadingId, setActiveHeadingId] = useState<string>('');
  const contentRef = useRef<HTMLDivElement>(null);

  const handleScrollToContentById = useCallback(async (targetId: string) => {
    const element = document.querySelector(`[data-heading-id="${targetId}"]`);
    if (element) {
      // 使用 scrollIntoView 实现平滑滚动
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // 检测当前可见的标题元素
  const updateActiveHeading = useCallback(() => {
    if (!contentRef.current) return;

    const headingElements = headings
      .map((heading) => document.querySelector(`[data-heading-id="${heading.id}"]`))
      .filter(Boolean);

    if (headingElements.length === 0) return;

    const contentRect = contentRef.current.getBoundingClientRect();
    let lastVisibleHeading: Element | null = null;

    for (const element of headingElements) {
      if (!element) continue;

      const rect = element.getBoundingClientRect();
      // 检查元素是否在内容区域的视口上方或视口内的上部
      // 相对于内容容器的顶部位置
      const relativeTop = rect.top - contentRect.top;

      if (relativeTop <= 120) {
        // 留一些缓冲区
        lastVisibleHeading = element;
      } else {
        break; // 一旦找到视口下方的元素，就停止
      }
    }

    if (lastVisibleHeading) {
      const headingId = lastVisibleHeading.getAttribute('data-heading-id');
      if (headingId && headingId !== activeHeadingId) {
        setActiveHeadingId(headingId);
      }
    }
  }, [headings, activeHeadingId]);

  // 监听滚动事件
  useEffect(() => {
    const handleScroll = () => {
      updateActiveHeading();
    };

    // 初始化时也要检查一次
    setTimeout(() => {
      updateActiveHeading();
      // 如果没有活跃标题，设置第一个为活跃
      if (!activeHeadingId && headings.length > 0) {
        setActiveHeadingId(headings[0].id);
      }
    }, 100);

    // 监听 content 区域的滚动事件
    const contentElement = contentRef.current;
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll, { passive: true });
      return () => {
        contentElement.removeEventListener('scroll', handleScroll);
      };
    }
  }, [updateActiveHeading]);

  const siderContent = useMemo(
    () => (
      <div className='sidebar-content'>
        <ul>
          {headings.map((heading) => (
            <li
              key={heading.id}
              className={`heading-level-${heading.level} ${activeHeadingId === heading.id ? 'active' : ''}`}
              onClick={() => handleScrollToContentById(heading.id)}
            >
              <a>{heading.text}</a>
            </li>
          ))}
        </ul>
      </div>
    ),
    [headings, handleScrollToContentById, activeHeadingId],
  );

  return (
    <Layout
      className='markdown-reader-container'
      style={{ minWidth: hiddenSider ? 1200 : 1440, ...style }}
    >
      {/* 左侧边栏 */}
      {!isRightSide && !hiddenSider && (
        <Sider
          width={250}
          style={{
            background: 'transparent',
            borderRight: '1px solid #e8e8e8',
            height: '100vh',
            position: 'sticky',
            top: 0,
            left: 0,
            overflow: 'auto',
          }}
        >
          {siderContent}
        </Sider>
      )}

      {/* 内容区域 */}
      <Content ref={contentRef} className='content' style={contentStyle}>
        <ReactMarkdown
          rehypePlugins={[rehypeSlug, remarkGfm]}
          components={{
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h1: ({ node, ...props }) => (
              <h1
                {...props}
                data-heading-id={props?.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h2: ({ node, ...props }) => (
              <h2
                {...props}
                data-heading-id={props.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h3: ({ node, ...props }) => (
              <h3
                {...props}
                data-heading-id={props.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h4: ({ node, ...props }) => (
              <h4
                {...props}
                data-heading-id={props.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h5: ({ node, ...props }) => (
              <h5
                {...props}
                data-heading-id={props.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            h6: ({ node, ...props }) => (
              <h6
                {...props}
                data-heading-id={props.children?.toString().toLowerCase().replace(/\s+/g, '-')}
              />
            ),
          }}
        >
          {content}
        </ReactMarkdown>
      </Content>

      {/* 右侧边栏 */}
      {isRightSide && !hiddenSider && (
        <Sider
          width={250}
          style={{
            background: 'transparent',
            borderLeft: '1px solid #e8e8e8',
            height: '100vh',
            position: 'sticky',
            top: 0,
            right: 0,
            overflow: 'auto',
          }}
        >
          {siderContent}
        </Sider>
      )}
    </Layout>
  );
};

export default ProMarkdown;
