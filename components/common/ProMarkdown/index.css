.markdown-reader-container {
  width: 100%;
  min-width: 1200px;
  height: 100vh;
  overflow: hidden;
}

.sidebar-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.sidebar-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-content li {
  margin-bottom: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-content li:hover {
  background-color: #f5f5f5;
}

.sidebar-content li a {
  text-decoration: none;
  color: #555;
  transition: color 0.2s ease;
  font-size: 13px;
  display: block;
}

.sidebar-content li a:hover {
  color: #1890ff;
}

.sidebar-content li.active {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.sidebar-content li.active a {
  color: #1890ff !important;
  font-weight: 600 !important;
}

/* 根据标题层级设置缩进 */
.heading-level-1 {
  margin-left: 0;
  font-weight: bold;
}

.heading-level-2 {
  margin-left: 8px;
}

.heading-level-3 {
  margin-left: 16px;
}

/* ...以此类推，可以添加更多层级 */

.content {
  padding: 24px;
  line-height: 1.6;
  height: 100vh;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
}

.content h1,
.content h2,
.content h3 {
  /* 为标题设置 padding-top，以确保点击链接时不会被顶部导航栏遮挡 */
  scroll-margin-top: 16px;
}
