import useDebouncedState from '@/hooks/useDebouncedState.ts';
import type { WidthSize } from '@/utils/widthSize.ts';
import { getWidthValue } from '@/utils/widthSize.ts';
import { LoadingOutlined } from '@ant-design/icons';
import type { SearchSelectProps } from '@ant-design/pro-field/lib/components/Select/SearchSelect';
import SearchSelect from '@ant-design/pro-field/lib/components/Select/SearchSelect';
import { useControllableValue, useInfiniteScroll, useMemoizedFn } from 'ahooks';
import { Flex, Spin, Typography } from 'antd';
import type { DefaultOptionType, LabeledValue } from 'antd/lib/select';
import { defaults, isArray, isEmpty } from 'es-toolkit/compat';
import React, { useMemo, useRef } from 'react';

function isMultiple(mode: string | undefined) {
  return mode === 'multiple' || mode === 'tags';
}

function isRawValue(value: any) {
  return !value || typeof value !== 'object';
}

function toArray<T>(value: T | T[]): T[] {
  if (isArray(value)) {
    return value;
  }
  return value !== undefined ? [value] : [];
}

function hasValue(value: any) {
  return value !== undefined && value !== null;
}

const ALL_OPTION_VALUE = '';

// types
export interface FieldNamesType {
  label?: string;
  value?: string;
}

export type RequestParams = {
  /**
   * the page number of the request, start from 0, same with all nezha page api
   */
  page?: number;

  /**
   * the page size of the request, just pass it to `request(url, { params })` it will transform pageSize to api limit param
   */
  pageSize?: number;

  /**
   * the keyword of the request
   */
  keyword?: string;

  [key: string]: any;
};

export type RequestResponse = {
  result?: DefaultOptionType[];
  total?: number;
  page?: number;
  noMore?: boolean;
};

export interface ProSelectProps<ValueType = any>
  extends Omit<SearchSelectProps<ValueType>, 'fetchData' | 'resetData' | 'request'> {
  /**
   * api request service to fetch data, the request result will be used as the options of the select
   * use keyword to search data
   */
  request?: (params: RequestParams) => Promise<RequestResponse | DefaultOptionType[]>;

  /**
   * the sarch keyword param name of the request, default to `keyword`
   */
  searchKeyword?: string;

  /**
   * the width of the select, support `xs`, `sm`, `md`, `lg`, `xl`(built-in size like pro-components) or a number
   */
  width?: WidthSize;

  /**
   * whether to show the "all" option
   * @default false
   */
  showAll?: boolean;

  /**
   * the label of the "all" option
   * @default i18n('common.all')
   */
  allOptionLabel?: string;

  /**
   * the page size of the request
   * @default 20
   */
  pageSize?: number;

  /**
   * the field names of the request result
   * @default { value: '_id', label: 'name' }
   */
  fieldNames?: FieldNamesType;

  /**
   * the threshold of the infinite scroll
   * @default 100
   */
  threshold?: number;

  /**
   * the debouncing time of the search
   * @default 300
   */
  debounceTime?: number;

  /**
   * the extra params of the request
   */
  params?: Record<string, any>;

  /**
   * whether to select the first option by default when data is loaded and no value is set
   * @default false
   */
  defaultSelectFirstOption?: boolean;

  /**
   * the value of the select, the value type is depending on the `labelInValue`, `fieldNames` and `mode` props
   */
  value?: any;

  /**
   * the change handler of the select, the value type is depending on the `labelInValue`, `fieldNames` and `mode` props
   */
  onChange?: (value: any, o?: any) => void;

  /**
   * the subtitle of the option,
   */
  subTitle?: string;
}

/**
 * Notice that this component is not compatible with the `ProFormItem` component, use `Form.Item` instead.
 * Because selecting the all option will set the value to undefined, this will cause the `ProFormItem` child value to be missing, then `useControllableValue` will not work by changing controlled mode to uncontrolled mode.
 */
export const ProSelect: React.FC<ProSelectProps> = ({
  request,
  width,
  style,
  showAll: showAllProps,
  allOptionLabel: allOptionLabelProps,
  pageSize = 20,
  threshold = 100,
  debounceTime = 300,
  params,
  labelInValue,
  subTitle,
  fieldNames: fieldNamesProps,
  mode: modeProps,
  searchKeyword,
  defaultSelectFirstOption = false,
  ...props
}) => {
  const allOptionLabel = allOptionLabelProps ?? 'All';
  // if allOptionLabel is provided, default to show the all option
  // @ts-ignore
  // eslint-disable-next-line no-constant-binary-expression
  const showAll = showAllProps ?? !!allOptionLabelProps ?? false;
  const popupRef = useRef<HTMLDivElement | null>(null);
  // default field names for request
  const fieldNames = defaults(fieldNamesProps, {
    value: request ? 'id' : 'value',
    label: request ? 'name' : 'label',
  });

  const [value, onChange] = useControllableValue(props, {
    valuePropName: 'value',
    trigger: 'onChange',
  });

  const [searchValue, setSearchValue] = useDebouncedState<string | undefined>(undefined, {
    wait: debounceTime,
  });

  // convert the value from the format of the ant design select option to the format of the request
  const normalize = useMemoizedFn((value: LabeledValue) => {
    // all option or undefined
    if ((showAll && value.value === ALL_OPTION_VALUE) || !hasValue(value.value)) {
      return undefined;
    }

    // the select value format is { label: string, value: string } if labelInValue
    return labelInValue
      ? {
          [fieldNames.value]: value.value,
          [fieldNames.label]: value.label,
        }
      : value.value;
  });

  const handleSelectChange = useMemoizedFn(
    (value: LabeledValue | LabeledValue[], option: DefaultOptionType | DefaultOptionType[]) => {
      const labeledValues = toArray(value);

      // multiple mode, and selected the all option
      if (showAll && labeledValues.some(({ value }) => !value)) {
        onChange(undefined, undefined);
        return;
      }

      const isMulti = isMultiple(modeProps);
      const normalizedValues = labeledValues.map(normalize);
      const options = toArray(option);
      onChange(isMulti ? normalizedValues : normalizedValues[0], isMulti ? options : options[0]);
    },
  );

  // force to use single mode if current value is all option
  const mode = showAll && isEmpty(value) ? undefined : modeProps;

  // convert the value to the format of the ant design select option
  const toSearchSelectValue = useMemoizedFn((value: any) => {
    // the select value format is { label: string, value: string } if labelInValue
    if (!isRawValue(value)) {
      return {
        label: value[fieldNames.label],
        value: value[fieldNames.value],
      };
    }
    return value;
  });

  const selectValue = useMemo(() => {
    if (isEmpty(value)) {
      return showAll ? ALL_OPTION_VALUE : value;
    }
    return isArray(value) ? value.map(toSearchSelectValue) : toSearchSelectValue(value);
  }, [value, showAll]);

  const initialLoad = useRef(true);
  const { data, loading, loadingMore, loadMore, noMore } = useInfiniteScroll(
    async function (d) {
      if (!request) {
        return {
          list: props.options ?? [],
          nextPage: 0,
          total: props.options?.length ?? 0,
        };
      }

      const response = await request({
        page: d?.nextPage ?? 0,
        pageSize,
        [searchKeyword ?? 'keyword']: searchValue,
        ...params,
      });

      if (isArray(response)) {
        return {
          list: response,
          total: response.length,
        };
      }

      return {
        list: (response.result ?? []) as any[],
        nextPage: (response.page ?? 0) + 1,
        total: response.total ?? 0,
        noMore: response.noMore,
      };
    },
    {
      isNoMore: (d) => {
        // 优先使用 API 返回的 noMore 字段
        if (d?.noMore !== undefined) {
          return d.noMore;
        }
        // 如果没有 noMore 字段，则根据返回的数据长度判断
        return (d?.list.length ?? 0) === 0;
      },
      reloadDeps: [searchValue, JSON.stringify(params), props.options],
      onSuccess: (d) => {
        if (defaultSelectFirstOption && initialLoad.current) {
          if (d?.list.length > 0 && isEmpty(value)) {
            const option = d.list[0];
            const value = labelInValue ? option : option[fieldNames.value];
            onChange(isMultiple(mode) ? [value] : value, isMultiple(mode) ? [option] : option);
          }
          initialLoad.current = false;
        }
      },
    },
  );

  const options = useMemo(() => {
    const options = [...(data?.list ?? [])].map((item: any) => ({
      ...item,
      value: item[fieldNames.value],
      label: item[fieldNames.label],
    }));
    if (showAll) {
      // add select all option at the beginning
      options.unshift({ value: ALL_OPTION_VALUE, label: allOptionLabel });
    }
    return options;
  }, [showAll, data?.list]);

  const onPopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (loading || loadingMore || !e.target) return;
    const el = e.target as HTMLElement;
    if (el.scrollHeight - el.scrollTop <= el.clientHeight + threshold && !noMore) {
      loadMore();
    }
    props.onPopupScroll?.(e);
  };

  const subTitleOptionRender = useMemoizedFn((item: any) => {
    const subTitleValue = subTitle ? item.data[subTitle] : undefined;
    return (
      <>
        <Typography.Text ellipsis>{item.label}</Typography.Text>
        {subTitleValue && (
          <Typography.Text style={{ display: 'block', fontSize: 12 }} type='secondary' ellipsis>
            {subTitleValue}
          </Typography.Text>
        )}
      </>
    );
  });

  return (
    <SearchSelect
      // default show search if a request is provided
      showSearch={!!request}
      allowClear
      labelRender={(item) => {
        // if the value is a mongo id, and the label is not provided, display blank
        // this is useful when the document is deleted and the value is still in the value
        if (!item.label && typeof item.value === 'string') {
          return '';
        }
        return item.label ?? item.value;
      }}
      {...props}
      labelInValue
      fetchData={setSearchValue}
      resetData={() => setSearchValue(undefined)}
      mode={mode}
      value={selectValue}
      onChange={handleSelectChange as any}
      style={{ width: getWidthValue(width), ...style }}
      loading={loading}
      popupRender={(menu) => {
        return loading ? (
          <Spin indicator={<LoadingOutlined spin />}>
            <div style={{ minHeight: 100 }} />
          </Spin>
        ) : (
          <div ref={popupRef}>
            {menu}
            <Flex justify={'center'}>
              {loadingMore && (
                <Spin indicator={<LoadingOutlined spin />} style={{ marginTop: 4 }} delay={100} />
              )}
            </Flex>
          </div>
        );
      }}
      onPopupScroll={onPopupScroll}
      options={options}
      // disable the filter option, filter by request only if the request is provided
      filterOption={request ? false : undefined}
      notFoundContent={loading ? <Spin size='small' /> : props?.notFoundContent}
      optionRender={props.optionRender ?? (subTitle ? subTitleOptionRender : undefined)}
    />
  );
};

export default ProSelect;
