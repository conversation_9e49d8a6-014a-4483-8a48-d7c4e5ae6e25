import MarkdownTypingPreview from '@/components/common/markdown-typing-preview';
import MarkdownView from '@/components/common/markdown-view';
import type { StreamMessage } from '@/components/detect-contact-popover/data';
import type { LookalikeCompanyProfile } from '@/components/lookalike-companies/data';
import AddToCrm from '@/components/lookalike-companies/lookalike-companies-modal/add-to-crm';
import { formatterLookalikeCompanyProfile } from '@/components/lookalike-companies/lookalike-companies-modal/dialogue';
import { bulkQueryAccountIdByNames } from '@/components/lookalike-companies/service';
import { getCurrentUser, getUserId } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request';
import { getZohoHeaderRequest } from '@/utils/zoho-request.ts';
import { Sender } from '@ant-design/x';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useMount, useRequest } from 'ahooks';
import { Alert, Button, Card, Flex, Modal, Space, Spin, Typography } from 'antd';
import { isArray } from 'es-toolkit/compat';
import { capitalize } from 'es-toolkit/string';
import React, { useMemo, useRef, useState } from 'react';
import MagicSVG from '~/assets/magic-colorful.svg';
import customCss from './index.css?raw';

let targetDocument: Document;
try {
  targetDocument = window.top!.document;
} catch {
  targetDocument = window.document;
}
const getStyleContainer = (): HTMLElement => targetDocument.head;

type ErrorType = 'not_found' | 'multiple_results' | 'exists' | 'unknown_error' | 'network';

const errorMsgMap: Record<ErrorType, any> = {
  not_found: 'Company not found',
  multiple_results: 'Multiple accounts found',
  exists: 'Account already exists',
  unknown_error: 'Unknown error',
  network: 'Connect error',
};

const SmartCreateAccount: React.FC = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [prompt, setPrompt] = useState<string | undefined>();
  // const [messages, setMessages] = useState<StreamMessage[]>([]);
  const [loadingMessageStream, setLoadingMessageStream] = useState<StreamMessage>();
  const [error, setError] = useState<
    | {
        type: ErrorType;
        message: string;
        data?: {
          name?: string;
          account_id?: string;
        };
      }
    | undefined
  >();
  const [multipleAccount, setMultipleAccount] = useState<LookalikeCompanyProfile[]>([]);
  // const [collapseKeys, setCollapseKeys] = useState<string[]>(['thinking-timeline']);

  useMount(() => {
    const styleElement = targetDocument.createElement('style');
    styleElement.id = 'lookalike-companies-modal-dialogue-custom-styles';
    styleElement.innerHTML = customCss;

    if (!targetDocument.getElementById(styleElement.id)) {
      getStyleContainer().appendChild(styleElement);
    }

    return () => {
      const existingStyle = targetDocument.getElementById(styleElement.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  });

  const ctrlRef = useRef<AbortController | null>(null);

  const {
    loading,
    run: handleSubmit,
    cancel: cancelSubmit,
  } = useRequest(
    async (userInput) => {
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }
      // setMessages([]);
      setLoadingMessageStream(undefined);

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;

      const userId = (await getUserId()) as string;
      const requestHeaders = await getZohoHeaderRequest();
      const userInfo = await getCurrentUser();

      const submitData = {
        user_input: userInput,
        user_id: userId,
        request_headers: requestHeaders,
        user_info: userInfo,
      };

      const streamURL = `${BASE_URL}/api/sales-agent/add-account-by-user-input`;
      await fetchEventSource(streamURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': X_API_KEY,
        },
        body: JSON.stringify(submitData),
        signal: ctrl.signal,
        openWhenHidden: true,
        onopen: async () => {
          setLoadingMessageStream({
            type: 'thinking',
            content: 'Start analysis',
            timestamp: new Date().toDateString(),
          });
          // setMessages([
          //   {
          //     type: 'thinking',
          //     content: 'Start analysis',
          //     timestamp: new Date().toDateString(),
          //   },
          // ]);
        },
        onmessage: async (event) => {
          const data: StreamMessage = JSON.parse(event.data);
          setLoadingMessageStream(data);
          // setMessages((prev) => {
          //   return [...prev, data];
          // });

          if (data.type === 'error') {
            const errorType = (data.content ?? 'unknown_error') as ErrorType;
            const errorMsg = errorMsgMap[errorType] || data.content;
            setError({
              type: errorType,
              message: errorMsg,
              data: data?.data,
            });
          }
          if (data.type === 'finish') {
            const multipleAccount = data?.data?.result;
            if (multipleAccount && isArray(multipleAccount)) {
              const accountNames = multipleAccount.map((item) => item.name).filter(Boolean);
              if (accountNames.length > 0) {
                const crmAccounts = await bulkQueryAccountIdByNames(accountNames);
                const updatedMultipleAccount = multipleAccount.map((item) => ({
                  ...item,
                  account_id: crmAccounts.find((acc: any) => acc.Account_Name === item.name)?.id,
                }));
                setMultipleAccount(updatedMultipleAccount);
              } else {
                setMultipleAccount(multipleAccount);
              }
            } else {
              const account_id = data?.data?.account_id;
              if (account_id) {
                window.location.href = `https://crm.zoho.com/crm/org663291548/tab/Accounts/${
                  account_id
                }`;
              }
              setOpen(false);
              setPrompt(undefined);
              setLoadingMessageStream(undefined);
              // setMessages([]);
            }
          }
        },
        onerror: (event) => {
          console.error('[inhand] SSE connection error', event);
          setError({
            type: 'network',
            message: 'SSE connection error',
          });
        },
        onclose: async () => {
          console.info('[inhand] SSE connection closed');
        },
      });
    },
    {
      manual: true,
      refreshDeps: [],
    },
  );

  const renderMultipleAccountCard = useMemo(() => {
    if (multipleAccount?.length > 1) {
      return (
        <div
          style={{
            width: 552,
            display: 'grid',
            gap: 8,
          }}
        >
          <Typography.Title
            level={5}
            style={{
              marginTop: 0,
            }}
          >
            We have found {multipleAccount?.length} companies:
          </Typography.Title>
          {multipleAccount.map((item: LookalikeCompanyProfile, index) => {
            const content = formatterLookalikeCompanyProfile(item);
            return (
              <Card
                // eslint-disable-next-line react/no-array-index-key
                key={`account-card-${index}`}
                size='small'
                title={
                  <div
                    style={{
                      fontWeight: 600,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 8,
                    }}
                  >
                    <span
                      style={{
                        fontSize: 18,
                        color: '#999',
                        flexShrink: 0,
                        lineHeight: '1.4',
                      }}
                    >
                      {index + 1}.
                    </span>
                    <Typography.Text
                      ellipsis
                      style={{ width: 500, fontSize: 18, lineHeight: '1.4' }}
                    >
                      {item.name}
                    </Typography.Text>
                  </div>
                }
                extra={
                  <AddToCrm
                    info={{ account_info: item, content: content }}
                    source_metadata={{ source: 'smart-create-account' }}
                    key={item.website}
                  />
                }
                style={{
                  width: 550,
                }}
              >
                <MarkdownView content={content} className='dialogue-markdown-view' />
              </Card>
            );
          })}
        </div>
      );
    }
  }, [multipleAccount]);

  return (
    <>
      <Button
        icon={
          <img src={MagicSVG} alt='magic' width={12} style={{ color: '#fff', marginTop: '-3px' }} />
        }
        onClick={() => {
          setOpen(true);
        }}
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        Smart Create
      </Button>
      {open && (
        <Modal
          title='Smart Create Account'
          open={open}
          onCancel={() => {
            setOpen(false);
            setMultipleAccount([]);
            setError(undefined);
            // setMessages([]);
            setLoadingMessageStream(undefined);
            setPrompt(undefined);
          }}
          footer={false}
          width={600}
          styles={{
            body: {
              maxHeight: 800,
              overflowY: 'auto',
              overflowX: 'hidden',
              minHeight: 120,
              display: 'grid',
              alignItems: 'center',
            },
          }}
          maskClosable={false}
        >
          {loading && (
            <Flex
              style={{ minHeight: 100, width: '100%' }}
              justify='center'
              align='center'
              vertical
            >
              <Spin spinning={loading} size='large' />
              <Typography.Text type='secondary'>
                <MarkdownTypingPreview markdownText={loadingMessageStream?.content ?? 'Loading'} />
              </Typography.Text>
            </Flex>
          )}
          <Space direction='vertical' size='middle' style={{ display: 'flex' }}>
            {/*{renderTimeLine}*/}
            {renderMultipleAccountCard}
            {error && (
              <Alert
                message={
                  error?.type === 'exists' ? (
                    <span>
                      This account ({error?.data?.name ?? ''}) already exists; you can click the{' '}
                      <Typography.Link
                        href={`https://crm.zoho.com/crm/org663291548/tab/Accounts/${error?.data?.account_id}`}
                        target='_blank'
                      >
                        View in CRM
                      </Typography.Link>{' '}
                      to view it.
                    </span>
                  ) : (
                    capitalize(error?.message)
                  )
                }
                type='error'
              />
            )}
            {multipleAccount?.length > 0 ? undefined : (
              <div>
                <div style={{ marginBottom: 16, opacity: 0.6 }}>
                  <Typography.Text style={{ fontSize: 11 }}>
                    Simply enter the company name or any identifying information you know, and I
                    will automatically complete the company profile and create the account for you.
                  </Typography.Text>
                </div>
                <Sender
                  loading={loading}
                  value={prompt}
                  onChange={(v) => {
                    // setMessages([]);
                    setMultipleAccount([]);
                    setLoadingMessageStream(undefined);
                    setError(undefined);
                    setPrompt(v);
                  }}
                  onSubmit={() => {
                    if (prompt) {
                      // setMessages([]);
                      setMultipleAccount([]);
                      setLoadingMessageStream(undefined);
                      setError(undefined);
                      handleSubmit(prompt);
                    }
                  }}
                  onCancel={() => {
                    ctrlRef.current?.abort();
                    cancelSubmit();
                  }}
                  readOnly={loading}
                  placeholder='Please enter as much information about the company as you can: name, address, website, industry, market segment, etc.'
                  autoSize={{
                    minRows: 2,
                    maxRows: 2,
                  }}
                />
                <Typography.Text style={{ marginTop: 16, opacity: 0.8 }}>
                  <Typography.Text style={{ fontSize: 11 }}>Please note:</Typography.Text>
                  <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                    There is no need to add prefixes such as “help me create” or “please help me.”
                  </Typography.Text>
                </Typography.Text>
                <Space direction='vertical' size={4} style={{ marginTop: 8, opacity: 0.6 }}>
                  <Typography.Text style={{ fontSize: 11 }}>Examples:</Typography.Text>
                  <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                    • Inhand Networks
                  </Typography.Text>
                  <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                    • https://www.inhand.com/
                  </Typography.Text>
                  <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                    • InHand Networks, based in the United States
                  </Typography.Text>
                </Space>
              </div>
            )}
          </Space>
        </Modal>
      )}
    </>
  );
};

export default SmartCreateAccount;
