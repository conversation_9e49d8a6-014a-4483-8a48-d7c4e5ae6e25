import React, { useCallback, useMemo, useRef, useState } from 'react';

import { ArrowDownOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { Sender } from '@ant-design/x';
import { useControllableValue, useMount, useRequest } from 'ahooks';
import {
  Alert,
  Button,
  Card,
  Descriptions,
  Flex,
  FloatButton,
  List,
  Space,
  Spin,
  Typography,
} from 'antd';

import type {
  EventStreamItem,
  LookalikeCompanyInfo,
  LookalikeCompanyProfile,
  TaskStatusType,
} from '../../data';
import {
  bulkQueryAccountIdByNames,
  getLookalikeCompaniesByThreadId,
  lookalikeCompaniesByAccountId,
  lookalikeCompaniesMoreByThreadId,
} from '../../service';
import AddToCrm from '../add-to-crm';

import { getAccountId } from '@/utils';
import { BASE_URL, X_API_KEY } from '@/utils/request.ts';
import type { EventSourceMessage } from '@microsoft/fetch-event-source';
import { fetchEventSource } from '@microsoft/fetch-event-source';

import MarkdownTypingPreview from '@/components/common/markdown-typing-preview';
import MarkdownView from '@/components/common/markdown-view';
import customCss from './index.css?raw';

let targetDocument: Document;
try {
  targetDocument = window.top!.document;
} catch {
  targetDocument = window.document;
}
const getStyleContainer = (): HTMLElement => targetDocument.head;

export const formatterLookalikeCompanyProfile = (accountInfo: LookalikeCompanyProfile): string => {
  if (!accountInfo) return '';
  let content = '';

  const infoColumns = [
    {
      key: 'website',
      label: 'Website',
    },
    {
      key: 'linkedin',
      label: 'LinkedIn',
    },
    {
      key: 'account_type',
      label: 'Account Type',
    },
    {
      key: 'location',
      label: 'Location',
    },
    {
      key: 'founded_year',
      label: 'Founded',
    },
    {
      key: 'phone',
      label: 'Phone',
    },
    {
      key: 'organization_revenue',
      label: 'Annual Revenue',
    },
    {
      key: 'industry',
      label: 'Industry',
    },
    {
      key: 'market_segments',
      label: 'Market Segments',
      render: (value: string[]) => value?.join(', '),
    },
  ];

  content = infoColumns
    .map((column) => {
      const currentValue = accountInfo[column.key as keyof LookalikeCompanyProfile];
      if (currentValue) {
        return `**${column.label}:** ${column.render ? column.render(currentValue as string[]) : currentValue}`;
      }
      return '';
    })
    .filter(Boolean)
    .join('\n');

  if (accountInfo.description) content += `\n> ${accountInfo.description}`;

  return content;
};

interface DialogueProps {
  refreshList?: (threadId?: string) => void;
  threadId?: string;
  onChangeThreadId?: (taskId?: string | undefined) => void;
}

const Dialogue: React.FC<DialogueProps> = ({ refreshList, threadId, onChangeThreadId }) => {
  const accountId = getAccountId();
  const profileListRef = useRef<HTMLDivElement>(null);
  const [showScrollArrow, setShowScrollArrow] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [lookalikeCompanyData, setLookalikeCompanyData] = useState<LookalikeCompanyInfo[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);

  const [thinkingEvent, setThinkingEvent] = useState<EventStreamItem | null>(null);
  const ctrlRef = useRef<AbortController | null>(null);

  const [activeThreadId, setActiveThreadId] = useControllableValue<string | undefined>({
    value: threadId,
    onChange: onChangeThreadId,
  });

  const [listLoading, setListLoading] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [moreLoading, setMoreLoading] = useState<boolean>(false);

  useMount(() => {
    const styleElement = targetDocument.createElement('style');
    styleElement.id = 'lookalike-companies-modal-dialogue-custom-styles';
    styleElement.innerHTML = customCss;

    if (!targetDocument.getElementById(styleElement.id)) {
      getStyleContainer().appendChild(styleElement);
    }

    return () => {
      const existingStyle = targetDocument.getElementById(styleElement.id);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  });

  const handleProfileListScroll = useCallback(() => {
    const el = profileListRef.current;
    if (!el) {
      return;
    }
    const { scrollTop, scrollHeight, clientHeight } = el;
    setShowScrollArrow(scrollTop + clientHeight < scrollHeight - 1);
  }, []);

  const scrollToTop = useCallback(() => {
    const el = profileListRef.current;
    if (!el) return;
    el.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  const scrollToBottom = useCallback(() => {
    const el = profileListRef.current;
    if (!el) return;
    el.scrollTo({
      top: el.scrollHeight,
      behavior: 'smooth',
    });
  }, []);

  useEffect(() => {
    const el = profileListRef.current;
    if (!el) return;
    handleProfileListScroll();
    el.addEventListener('scroll', handleProfileListScroll);
    return () => {
      el.removeEventListener('scroll', handleProfileListScroll);
    };
  }, [handleProfileListScroll, lookalikeCompanyData]);

  const { run: handleReconnectSSE } = useRequest(
    async ({ runningTaskIds, threadId }) => {
      setMoreLoading(true);
      for (const runningTaskId of runningTaskIds) {
        await startEventSource({
          task_id: runningTaskId,
          thread_id: threadId,
          task_status: 'pending',
        });
      }
      setMoreLoading(false);
    },
    {
      manual: true,
    },
  );

  const {
    loading: historyThreadLoading,
    run: fetchThreadProfile,
    cancel: cancelFetchProfile,
    data: threadProfileData,
  } = useRequest(
    async (threadId) => {
      if (threadId) {
        setListLoading(true);
        const {
          data = [],
          running_task_ids,
          ...other
        } = await getLookalikeCompaniesByThreadId(threadId);
        // 处理 data，构建 content 展示内容
        const processed_data: LookalikeCompanyInfo[] = data.map((item: any) => {
          return {
            account_info: item.account_info ?? item,
            content: item.content ?? formatterLookalikeCompanyProfile(item),
          };
        });
        // 检查accoun是否在CRM中存在
        const accountNames = processed_data.map((item) => item.account_info.name).filter(Boolean);
        if (accountNames.length > 0) {
          const crmAccounts = await bulkQueryAccountIdByNames(accountNames);
          // 更新account_id
          const updatedProcessedData = processed_data.map((item) => {
            const crmAccountId = crmAccounts.find(
              (acc: any) => acc.Account_Name === item.account_info.name,
            )?.id;
            return crmAccountId
              ? { ...item, account_info: { ...item.account_info, account_id: crmAccountId } }
              : item;
          });
          setLookalikeCompanyData(updatedProcessedData);
        } else {
          setLookalikeCompanyData(processed_data);
        }
        if (data?.length === 0) {
          setLoading(true);
          setPrompt(other?.user_query ?? '');
        }
        if (running_task_ids?.length > 0) {
          handleReconnectSSE({
            runningTaskIds: running_task_ids,
            threadId,
          });
        }
        if (other?.error) {
          setErrorMessage(other.error);
          setLoading(false);
        }
        setListLoading(false);
        return {
          ...other,
          data,
        };
      } else {
        setLookalikeCompanyData([]);
        return undefined;
      }
    },
    {
      manual: true,
      onError: (e) => {
        setErrorMessage(e.message ?? JSON.stringify(e));
        setListLoading(false);
        setLoading(false);
      },
    },
  );

  const startEventSource = useCallback(
    async ({
      task_id,
      thread_id,
      task_status,
      isMore = false,
    }: {
      task_id: string;
      thread_id: string;
      task_status?: TaskStatusType;
      isMore?: boolean;
    }) => {
      if (ctrlRef.current) {
        ctrlRef.current.abort();
      }

      const ctrl = new AbortController();
      ctrlRef.current = ctrl;
      setThinkingEvent({
        event: 'pending',
        status: task_status ?? 'pending',
        content: 'Ready to start',
      });

      // 根据是否是更多勘探来设置不同的loading状态
      if (isMore) {
        setMoreLoading(true);
      } else {
        setLoading(true);
      }

      await fetchEventSource(`${BASE_URL}/api/sales-agent/tasks/${task_id}/stream`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': X_API_KEY,
        },
        signal: ctrl.signal,
        onopen: async () => {
          setThinkingEvent({
            event: 'thinking',
            status: 'thinking',
            content: 'The current task is in the queue.',
          });
        },
        onmessage: async (event: EventSourceMessage) => {
          const parsedData: EventStreamItem = JSON.parse(event.data);

          if (
            parsedData?.type &&
            ['thinking', 'completed', 'finish'].includes(parsedData?.type as string)
          ) {
            if (parsedData?.type === 'thinking' || parsedData?.type === 'status') {
              setThinkingEvent(parsedData);
            }
            if (parsedData?.type === 'completed' || parsedData?.type === 'finish') {
              setThinkingEvent(parsedData);
              setActiveThreadId(thread_id);
              if (!(parsedData?.result as any)?.data?.length) {
                setErrorMessage('No results');
              }
            }
          } else if (parsedData.type === 'data') {
            const rawData = parsedData.data;
            if (rawData && typeof rawData === 'object') {
              const newAccount = rawData as LookalikeCompanyProfile;
              const crmAccounts = await bulkQueryAccountIdByNames([newAccount.name]);
              setLookalikeCompanyData((prev) => {
                // 去重
                if (prev.some((item) => item.account_info.name === newAccount.name)) {
                  return prev;
                }
                const crmAccountId = crmAccounts.find(
                  (acc: any) => acc.Account_Name === newAccount.name,
                )?.id;
                return [
                  ...prev,
                  {
                    account_info: {
                      ...newAccount,
                      account_id: crmAccountId,
                    },
                    content: formatterLookalikeCompanyProfile(newAccount),
                  },
                ];
              });
            }
          } else if (parsedData.type === 'error') {
            setThinkingEvent({
              event: 'error',
              status: 'failed',
              content: parsedData.content || 'An error occurred',
            });
            refreshList?.();
            setErrorMessage(parsedData.content || 'An error occurred');
          }
        },
        onclose: async () => {
          // 根据是否是更多勘探来清除不同的loading状态
          if (isMore) {
            setMoreLoading(false);
          } else {
            setLoading(false);
          }
          ctrl.abort();
          ctrlRef.current = null;
        },
        onerror: () => {
          // 根据是否是更多勘探来清除不同的loading状态
          if (isMore) {
            setMoreLoading(false);
          } else {
            setLoading(false);
          }
          ctrl.abort();
          ctrlRef.current = null;
        },
      });
    },
    [fetchThreadProfile, setActiveThreadId, refreshList],
  );

  const { run: handleLookalike, cancel: cancelLookalike } = useRequest(
    async ({ prompt }: { prompt: string }) => {
      setLoading(true);
      const { task_id, error, thread_id, task_status } = await lookalikeCompaniesByAccountId({
        account_id: accountId,
        user_query: prompt,
      });
      if (thread_id) {
        setActiveThreadId(thread_id);
        refreshList?.();
      }
      if (task_id) {
        await startEventSource({
          task_id,
          thread_id,
          task_status,
          isMore: false,
        });
      }
      if (error) {
        setErrorMessage(error);
      }
      setLoading(false);
    },
    {
      manual: true,
      refreshDeps: [accountId, prompt],
      onError: (e) => {
        setErrorMessage(e.message ?? JSON.stringify(e));
        setLoading(false);
      },
    },
  );

  const { run: handleMore } = useRequest(
    async () => {
      if (!activeThreadId) {
        return;
      }
      setMoreLoading(true);
      const { task_id, error, thread_id, task_status } =
        await lookalikeCompaniesMoreByThreadId(activeThreadId);

      scrollToBottom();

      if (task_id) {
        await startEventSource({
          task_id,
          thread_id,
          task_status,
          isMore: true,
        });
      }
      if (error) {
        setErrorMessage(error);
      }
      setMoreLoading(false);
    },
    {
      manual: true,
      refreshDeps: [activeThreadId],
    },
  );

  useEffect(() => {
    setLoading(false);
    cancelFetchProfile();
    cancelLookalike?.();
    scrollToTop();
    setPrompt('');
    setThinkingEvent(null);
    ctrlRef?.current?.abort();
    setErrorMessage(undefined);
    setLookalikeCompanyData([]);
    if (activeThreadId) {
      fetchThreadProfile(activeThreadId);
    }
  }, [activeThreadId, fetchThreadProfile, cancelFetchProfile, cancelLookalike, scrollToTop]);

  const dialogLoading = useMemo(
    () =>
      loading ||
      historyThreadLoading ||
      moreLoading ||
      (!!activeThreadId && lookalikeCompanyData.length === 0 && !errorMessage),
    [
      loading,
      historyThreadLoading,
      moreLoading,
      activeThreadId,
      lookalikeCompanyData.length,
      errorMessage,
    ],
  );

  const handleRetry = useCallback(() => {
    setErrorMessage(undefined);
    setMoreLoading(true);
    handleMore();
  }, [handleMore]);

  const getLoadingText = useCallback(() => {
    if (listLoading) {
      return 'Loading...'; // 页面初始加载数据时
    }
    return thinkingEvent?.content || 'Ready to start';
  }, [listLoading, thinkingEvent]);

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
      }}
      className='lookalike-companies-dialogue'
    >
      {lookalikeCompanyData && lookalikeCompanyData?.length > 0 ? (
        <div style={{ position: 'relative', height: 602 }}>
          <List
            className='list-container'
            header={
              <Descriptions
                column={1}
                items={[
                  {
                    label: 'Ideal Customer Profile',
                    children: threadProfileData?.user_query,
                  },
                ]}
              />
            }
            // @ts-ignore
            ref={profileListRef as HTMLDivElement}
            loading={listLoading}
            style={{ height: '100%', overflow: 'auto' }}
            dataSource={lookalikeCompanyData}
            renderItem={(item: LookalikeCompanyInfo, index: number) => {
              return (
                <List.Item
                  style={{
                    padding: 0,
                    borderRadius: 8,
                    marginBottom: 16,
                    boxShadow:
                      '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
                  }}
                >
                  <Card
                    size='small'
                    title={
                      <div
                        style={{
                          fontWeight: 600,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 8,
                        }}
                      >
                        <span
                          style={{
                            fontSize: 18,
                            color: '#999',
                            flexShrink: 0,
                            lineHeight: '1.4',
                          }}
                        >
                          {index + 1}.
                        </span>
                        <Typography.Text
                          ellipsis
                          style={{ width: 500, fontSize: 18, lineHeight: '1.4' }}
                        >
                          {item.account_info.name}
                        </Typography.Text>
                      </div>
                    }
                    extra={
                      <AddToCrm
                        info={item}
                        source_metadata={{
                          source: 'lookalike-companies',
                          thread_id: activeThreadId,
                          account_id: accountId,
                        }}
                        key={item.account_info.website}
                      />
                    }
                    style={{
                      width: '100%',
                    }}
                  >
                    <MarkdownView content={item.content} className='dialogue-markdown-view' />
                  </Card>
                </List.Item>
              );
            }}
            loadMore={
              moreLoading ? (
                <Flex
                  vertical
                  style={{
                    height: 460,
                    width: '100%',
                  }}
                  justify={'center'}
                  align={'center'}
                >
                  <Spin spinning size='large' />
                  <Typography.Text
                    type='secondary'
                    style={{
                      maxHeight: 400,
                      padding: '12px 16px',
                    }}
                  >
                    <MarkdownTypingPreview markdownText={getLoadingText()} />
                  </Typography.Text>
                </Flex>
              ) : (
                <Space
                  direction='vertical'
                  style={{
                    width: '100%',
                  }}
                >
                  {errorMessage && (
                    <Alert
                      type='error'
                      message='An error occurred while fetching data. Please check the error message below.'
                      description={errorMessage}
                      showIcon={true}
                      banner={true}
                      style={{
                        borderRadius: 8,
                        margin: '0 12px',
                      }}
                    />
                  )}
                  <Flex justify='center' align='center'>
                    <Button
                      icon={
                        <DoubleRightOutlined
                          style={{
                            transform: 'rotate(90deg)',
                          }}
                        />
                      }
                      onClick={() => {
                        handleMore();
                        setErrorMessage(undefined);
                      }}
                    >
                      More
                    </Button>
                  </Flex>
                </Space>
              )
            }
          />
          {showScrollArrow && (
            <FloatButton
              icon={<ArrowDownOutlined />}
              shape='circle'
              style={{
                position: 'absolute',
                left: 'calc(50% - 20px)',
                bottom: 20,
              }}
              onClick={scrollToBottom}
            />
          )}
        </div>
      ) : (
        <div
          style={{
            height: '100%',
            alignItems: 'center',
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
          }}
        >
          {dialogLoading ? (
            <Flex
              vertical
              style={{
                height: '100%',
                width: '100%',
              }}
              justify={'center'}
              align={'center'}
            >
              <Spin spinning size='large' />
              <Typography.Text
                type='secondary'
                style={{
                  maxHeight: 'calc(100% - 62px)',
                  padding: '12px 16px',
                }}
              >
                <MarkdownTypingPreview markdownText={getLoadingText()} />
              </Typography.Text>
            </Flex>
          ) : errorMessage ? (
            <Typography>
              <Typography.Title level={5} type='danger'>
                An error occurred while fetching data. Please try again.{' '}
                <Button
                  onClick={handleRetry}
                  type='link'
                  style={{
                    marginLeft: 8,
                  }}
                >
                  Retry
                </Button>
              </Typography.Title>
              <Typography.Paragraph
                copyable
                style={{
                  fontSize: 12,
                  color: 'rgba(0,0,0,0.6)',
                }}
              >
                {errorMessage}
              </Typography.Paragraph>
            </Typography>
          ) : !activeThreadId ? (
            <div
              style={{
                height: '100%',
              }}
            >
              <div style={{ marginBottom: 8, opacity: 0.6 }}>
                <Typography.Text style={{ fontSize: 11 }}>
                  Please describe the enterprise characteristics you want to find. Help the system
                  find potential customers similar to your selected companies. We recommend
                  approaching from the following perspectives:
                </Typography.Text>
              </div>

              <Space
                direction='vertical'
                size={4}
                style={{
                  marginBottom: 60,
                  opacity: 0.6,
                }}
              >
                <Typography.Text style={{ fontSize: 11 }}>
                  <Typography.Text
                    style={{
                      fontSize: 11,
                    }}
                  >
                    Potential Products Needed:
                  </Typography.Text>{' '}
                  What products might they need? (e.g., Industrial Gateways, Cellular Routers, IoT
                  Modules...)
                </Typography.Text>
                <Typography.Text style={{ fontSize: 11 }}>
                  <Typography.Text
                    style={{
                      fontSize: 11,
                    }}
                  >
                    Application Scenarios:
                  </Typography.Text>{' '}
                  What scenarios will these products be used for? (e.g., Remote Monitoring,
                  Predictive Maintenance, Energy Management...)
                </Typography.Text>
              </Space>

              <Sender
                loading={dialogLoading}
                value={prompt}
                onChange={setPrompt}
                onSubmit={() => {
                  handleLookalike({ prompt });
                  setErrorMessage(undefined);
                }}
                placeholder='Companies needing edge gateways for remote equipment monitoring and predictive maintenance in manufacturing facilities to reduce downtime and optimize operations.'
                autoSize={{
                  minRows: 4,
                  maxRows: 6,
                }}
                style={{
                  borderRadius: 8,
                  // border: '1px solid #1890ff',
                  boxShadow: 'none',
                }}
              />

              <Space direction='vertical' size={4} style={{ marginTop: 12, opacity: 0.6 }}>
                <Typography.Text style={{ fontSize: 11 }}>Examples:</Typography.Text>
                <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                  • Air compressor manufacturers who use edge gateways for remote monitoring and
                  predictive maintenance
                </Typography.Text>
                <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                  • System integrators providing monitoring platforms for power plants, requiring
                  industrial routers for data collection and remote access
                </Typography.Text>
                <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                  • CT and MRI manufacturers using IoT modules for equipment remote monitoring and
                  after-sales cloud services
                </Typography.Text>
                <Typography.Text style={{ fontSize: 10, marginLeft: 8 }}>
                  • Smart public transport companies requiring cellular routers for vehicle
                  networking and real-time data upload
                </Typography.Text>
              </Space>
            </div>
          ) : (
            <Flex
              vertical
              style={{
                height: '100%',
                width: '100%',
              }}
              justify={'center'}
              align={'center'}
            >
              <Spin spinning size='large' />
              <Typography.Text
                type='secondary'
                style={{
                  maxHeight: 'calc(100% - 62px)',
                  padding: '12px 16px',
                }}
              >
                <MarkdownTypingPreview markdownText={getLoadingText()} />
              </Typography.Text>
            </Flex>
          )}
        </div>
      )}
    </div>
  );
};

Dialogue.displayName = 'Dialogue';

export default Dialogue;
