import React from 'react';

import { CheckCircleOutlined, CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, message, Space, Tooltip, Typography } from 'antd';

import type { AddAccountInfoType } from '../../service';
import { addAccountToCRM } from '../../service';

import type { LookalikeCompanyInfo } from '../../data';

import { getUserId } from '@/utils';
import { getZohoHeaderRequest } from '@/utils/zoho-request.ts';
import { capitalize } from 'es-toolkit/string';

interface AddToCrmProps {
  info: LookalikeCompanyInfo;
  source_metadata: any;
  onSuccess?: (data?: any) => void;
}

const AccountViewTooltip: React.FC<{
  title: string;
  accountId: string;
}> = ({ title, accountId }) => (
  <Space>
    <Tooltip placement='left' title={title} getPopupContainer={(node) => node.parentElement!}>
      <CheckCircleOutlined
        style={{
          color: '#52c41a',
          cursor: 'pointer',
          marginLeft: 8,
        }}
      />
    </Tooltip>
    <Typography.Link href={`/crm/org663291548/tab/Accounts/${accountId}`} target='_blank'>
      View
    </Typography.Link>
  </Space>
);

const AddToCrm: React.FC<AddToCrmProps> = ({ info, source_metadata, onSuccess }) => {
  const [messageApi, contextHolder] = message.useMessage();

  const {
    loading,
    run: handleAdd,
    data,
  } = useRequest(
    async () => {
      const user_id = (await getUserId()) as string;
      const data: AddAccountInfoType = {
        user_id,
        account_info: info.account_info,
        request_headers: await getZohoHeaderRequest(),
        source_metadata,
      };
      const resp = await addAccountToCRM(data);
      if (onSuccess) {
        onSuccess(data);
      }
      if (!resp?.account_id) {
        messageApi.error(capitalize(resp?.detail) || 'Added to CRM failed');
      }
      return resp;
    },
    {
      manual: true,
      refreshDeps: [info],
    },
  );

  return (
    <>
      {info?.account_info?.account_id ? (
        /* 已存在CRM中 */
        <AccountViewTooltip title={'Already in CRM'} accountId={info.account_info.account_id} />
      ) : data && data?.account_id ? (
        /* 添加成功 */
        <AccountViewTooltip title={'Added to CRM successfully'} accountId={data.account_id} />
      ) : (
        /* 未添加时，显示添加按钮 */
        <Button
          type='text'
          loading={loading}
          icon={<PlusOutlined />}
          size='small'
          onClick={() => handleAdd()}
        >
          Add to CRM
        </Button>
      )}
      {data && !data?.account_id && (
        /* 添加失败，显示错误信息 */
        <Tooltip
          placement='left'
          title={capitalize(data?.detail) || 'Added to CRM failed'}
          getPopupContainer={(node) => node.parentElement!}
        >
          <CloseCircleOutlined
            style={{
              color: '#ff4d4f',
              cursor: 'pointer',
              marginLeft: 8,
            }}
          />
        </Tooltip>
      )}
      {contextHolder}
    </>
  );
};

export default AddToCrm;
