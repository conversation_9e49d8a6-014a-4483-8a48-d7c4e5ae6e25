import React, { useState } from 'react';

import LookalikeCompaniesModal from './lookalike-companies-modal';

const LookalikeCompanies: React.FC = () => {
  const [lookalikeCompaniesModalOpen, setLookalikeCompaniesModalOpen] = useState(false);

  return (
    <>
      <div onClick={() => setLookalikeCompaniesModalOpen(true)}>Lookalike Companies</div>
      {lookalikeCompaniesModalOpen && (
        <LookalikeCompaniesModal
          open={lookalikeCompaniesModalOpen}
          onFinish={() => setLookalikeCompaniesModalOpen(false)}
          onCancel={() => setLookalikeCompaniesModalOpen(false)}
        />
      )}
    </>
  );
};

export default LookalikeCompanies;
