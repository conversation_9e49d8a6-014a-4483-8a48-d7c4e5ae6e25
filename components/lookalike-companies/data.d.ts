import type { TaskStatusType } from '../detect-contacts/data';

export { TaskStatusType };

export interface LookalikeCompanyInfo {
  content: string;
  account_info: LookalikeCompanyProfile;
}

export interface LookalikeCompanyProfile {
  account_id?: string;
  name: string;
  website: string;
  linkedin: string;
  account_type: string;
  phone: string;
  industry: string;
  market_segments: string[];
  founded_year: number;
  organization_revenue: string;
  location: string;
  territory: string;
  address_state: string;
  description: string;
}

export interface HistoryItem {
  _id: string;
  title?: string;
}

export interface EventStreamItem {
  event: string;
  status: TaskStatusType;
  message?: string;
  content?: string;

  [Key: string]: unknown;
}
