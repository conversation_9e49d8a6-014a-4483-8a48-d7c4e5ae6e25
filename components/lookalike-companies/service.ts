import { getCurrentUser } from '@/utils';
import request from '@/utils/request.ts';
import { zohoRequest } from '@/utils/zoho-request.ts';
import type { LookalikeCompanyProfile } from './data';

export interface SubmitDataType {
  account_id: string;
  user_query: string;
}

export const lookalikeCompaniesByAccountId = async (data: SubmitDataType) => {
  const currentUser = await getCurrentUser();
  return await request('/api/sales-agent/lookalike-companies', {
    method: 'post',
    data: {
      ...data,
      current_user: currentUser,
    },
  });
};

export const lookalikeCompaniesMoreByThreadId = async (thread_id: string) => {
  const currentUser = await getCurrentUser();
  return await request('/api/sales-agent/lookalike-companies/more', {
    method: 'post',
    data: {
      thread_id,
      current_user: currentUser,
    },
  });
};

export const getLookalikeCompaniesByAccountId = async (accountId: string) => {
  return await request(`/api/sales-agent/lookalike-companies/${accountId}/threads`, {
    method: 'get',
  });
};

export const getLookalikeCompaniesByThreadId = async (threadId: string) => {
  return await request(`/api/sales-agent/lookalike-companies/thread/${threadId}`, {
    method: 'get',
  });
};

export interface AddAccountInfoType {
  user_id: string;
  account_info: LookalikeCompanyProfile;
  request_headers: any;
  source_metadata: any;
}

export const addAccountToCRM = async (data: AddAccountInfoType) => {
  return await request('/api/sales-agent/add-account', {
    method: 'post',
    data,
  });
};

export const bulkQueryAccountIdByNames = async (account_names: string[]) => {
  // 需要对name中的小括号和逗号进行转义
  const escapeNames = account_names.map((name) => name.replace(/[(),]/g, '\\$&'));
  const resp = await zohoRequest('/crm/v2.2/Accounts/search', {
    method: 'get',
    params: {
      fields: 'id,Account_Name',
      criteria: `(Account_Name:in:${escapeNames.join(',')})`,
    },
  });
  return resp?.data || [];
};
