import { getCurrentUser } from '@/utils';
import request from '@/utils/request.ts';
import { zohoRequest } from '@/utils/zoho-request.ts';

export const getAccountInfo = async (accountId: string) => {
  const resp = await zohoRequest(`/crm/v2.2/Accounts/${accountId}`, {
    method: 'get',
  });
  return resp?.data?.[0];
};

export const enrichAccount = async (accountId: string) => {
  const userInfo = await getCurrentUser();
  const enrichData = await request(`/api/sales-agent/account/${accountId}/enrich`, {
    method: 'get',
    params: {
      username: userInfo?.username,
      email: userInfo?.email,
    },
  });
  const zohoData = await getAccountInfo(accountId);
  return { enrichData, zohoData };
};

export const updateAccountInfo = async (accountId: string, account: Record<string, any>) => {
  const submitData = {
    data: [
      {
        id: accountId,
        ...account,
      },
    ],
    skip_mandatory: false,
  };
  return await zohoRequest(`/crm/v2.2/Accounts/${accountId}`, {
    method: 'PUT',
    data: submitData,
  });
};
