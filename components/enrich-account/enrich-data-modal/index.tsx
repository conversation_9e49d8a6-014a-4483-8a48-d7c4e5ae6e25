import React, { type Key, useState } from 'react';

import { useMemoizedFn, useRequest } from 'ahooks';
import type { ModalProps, TableProps } from 'antd';
import { Alert, Button, Empty, Flex, message, Modal, Spin, Table, Typography } from 'antd';
import type { TableRowSelection } from 'antd/es/table/interface';

import { getAccountId } from '@/utils';
import { isEmpty, isEqual } from 'es-toolkit/compat';

import { enrichAccount, updateAccountInfo } from '../service';

const enrichKeyMap = [
  {
    enrichKey: 'name',
    zohoKey: 'Account_Name',
    label: 'Account Name',
  },
  {
    enrichKey: 'account_type',
    zohoKey: 'Account_Type',
    label: 'Account Type',
  },
  {
    enrichKey: 'website',
    zohoKey: 'Website',
    label: 'Website',
  },
  {
    enrichKey: 'account_email',
    zohoKey: 'Account_Email',
    label: 'MainEmail',
  },
  {
    enrichKey: 'secondary_account_email',
    zohoKey: 'Secondary_Account_Email',
    label: 'Secondary Account Email',
  },
  {
    enrichKey: 'phone',
    zohoKey: 'Phone',
    label: 'Phone',
  },
  {
    enrichKey: 'industry',
    zohoKey: 'Industry',
    label: 'Industry',
  },
  {
    enrichKey: 'secondary_industry',
    zohoKey: 'Secondary_Industry',
    label: 'Secondary Industry',
  },
  {
    enrichKey: 'market_segments',
    zohoKey: 'Market_Segment_New',
    label: 'Market Segment',
  },
  {
    enrichKey: 'territory',
    zohoKey: 'Country_Territory',
    label: 'Billing Country/Territory',
  },
  {
    enrichKey: 'address_state',
    zohoKey: 'Billing_State',
    label: 'Billing State',
  },
  {
    enrichKey: 'billing_city',
    zohoKey: 'Billing_City',
    label: 'Billing City',
  },
  {
    enrichKey: 'billing_street',
    zohoKey: 'Billing_Street',
    label: 'Billing Street',
  },
  {
    enrichKey: 'billing_street_2',
    zohoKey: 'Billing_Street_2',
    label: 'Billing Street 2',
  },
  {
    enrichKey: 'billing_code',
    zohoKey: 'Billing_Code',
    label: 'Billing Code',
  },
  {
    enrichKey: 'description',
    zohoKey: 'Description',
    label: 'Description',
  },
];

interface DataType {
  enrichKey: string;
  zohoKey: string;
  enrichValue: string | string[];
  zohoValue: string | string[];
  zohoLabel: string;
  isSame: boolean;
  key: string;
}

const generateCompareTableData = (
  enrichData: Record<string, any>,
  zohoData: Record<string, any>,
) => {
  const dataSource: DataType[] = [];

  enrichKeyMap.forEach((item) => {
    const enrichValue = enrichData?.[item.enrichKey];
    const zohoValue = zohoData?.[item.zohoKey];
    // 没有获取到值的字段不展示
    if (isEmpty(enrichValue)) {
      return;
    }
    // 相同值的字段不展示
    const isSame = isEqual(enrichValue, zohoValue);
    if (!isSame) {
      dataSource.push({
        key: item.zohoKey,
        enrichKey: item.enrichKey,
        zohoKey: item.zohoKey,
        zohoLabel: item.label,
        enrichValue: enrichValue,
        zohoValue: zohoValue,
        isSame,
      });
    }
  });

  return dataSource;
};

type EnrichDataModalProps = {
  onOk?: () => void;
} & ModalProps;

const EnrichDataModal: React.FC<EnrichDataModalProps> = ({ open, onCancel, onOk }) => {
  const accountId = getAccountId();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[] | undefined>([]);
  const [selectedRows, setSelectedRows] = useState<DataType[]>([]);
  const [messageApi, contextHolder] = message.useMessage();

  const {
    loading,
    data = {},
    run: enrichRun,
    cancel: handleCancel,
  } = useRequest(
    async () => {
      if (accountId) {
        const { enrichData, zohoData } = await enrichAccount(accountId);
        const dataSource = generateCompareTableData(enrichData, zohoData);
        if (dataSource.length > 0) {
          setSelectedRowKeys(dataSource.map((item) => item.key));
          setSelectedRows(dataSource);
        }
        return { dataSource };
      }
    },
    {
      refreshDeps: [accountId],
    },
  );

  const { dataSource = [] } = data as { dataSource: DataType[]; error?: string };

  const { loading: updating, run: handleUpdateAccount } = useRequest(
    async () => {
      // 根据 selectedRowKeys 更新 accountInfo
      const accountInfo: Record<string, any> = {};
      selectedRows.forEach((item) => {
        accountInfo[item.zohoKey] = item.enrichValue;
      });

      const res = await updateAccountInfo(accountId, accountInfo);
      if (res?.data?.[0]?.code === 'SUCCESS') {
        messageApi.success(
          'Update Account Info Success, please refresh the page to see the latest data',
        );
        onOk?.();
        window.location.reload();
      } else {
        messageApi.error('Update Account Info Failed');
      }
    },
    {
      manual: true,
      refreshDeps: [selectedRowKeys, selectedRows, accountId],
    },
  );

  const onSelectChange = (newSelectedRowKeys: Key[], selectedRows: DataType[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const onRowClick = useMemoizedFn((record: DataType) => {
    const key = record.zohoKey as Key;

    if (!selectedRowKeys) return;

    const cloneSelectedRowKeys = [...selectedRowKeys];
    let cloneSelectedRows = [...selectedRows];

    if (cloneSelectedRowKeys.includes(key)) {
      // 取消选中
      const keyIndex = cloneSelectedRowKeys.indexOf(key);
      cloneSelectedRowKeys.splice(keyIndex, 1);
      cloneSelectedRows = cloneSelectedRows.filter((row) => row.zohoKey !== key);
    } else {
      // 选中
      cloneSelectedRowKeys.push(key);
      cloneSelectedRows = [...cloneSelectedRows, record];
    }

    setSelectedRowKeys(cloneSelectedRowKeys);
    setSelectedRows(cloneSelectedRows);
  });

  const rowSelection: TableRowSelection<DataType> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const columns: TableProps<DataType>['columns'] = [
    {
      title: 'CRM Field Name',
      dataIndex: 'zohoLabel',
      width: 160,
      key: 'zohoLabel',
    },
    {
      title: 'Existing Information',
      dataIndex: 'zohoValue',
      width: 160,
      key: 'zohoValue',
      render: (value: any) => {
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        return value;
      },
    },
    {
      title: 'Enriched Information',
      dataIndex: 'enrichValue',
      width: 160,
      key: 'enrichValue',
      render: (value: any) => {
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        return value;
      },
    },
  ];

  return (
    <Modal
      title='Data Enrichment'
      open={open}
      onCancel={(e) => {
        handleCancel();
        onCancel?.(e);
      }}
      onOk={() => handleUpdateAccount()}
      width={1000}
      maskClosable={false}
      okText={updating ? 'Updating' : 'Update'}
      cancelText='Cancel'
      okButtonProps={{ disabled: selectedRowKeys?.length === 0, loading: updating }}
      styles={{
        body: {
          maxHeight: '60vh',
          overflowY: 'auto',
        },
      }}
    >
      {loading ? (
        <Flex
          justify='center'
          align='center'
          vertical
          style={{
            height: 200,
            width: '100%',
          }}
        >
          <Spin />
          <Typography.Text
            className='ant-spin-text'
            style={{
              fontSize: 14,
              textShadow: '0 1px 2px #fff',
              color: '#1677ff',
              marginTop: 14,
            }}
          >
            We are retrieving information from multiple sources. Please wait.
          </Typography.Text>
        </Flex>
      ) : (data as { error?: string })?.error ? (
        <Flex justify='center' align='center'>
          <Alert
            message={
              <span>
                Error occurred: {(data as { error?: string })?.error}. You can click{' '}
                <Button onClick={() => enrichRun()}>Retry</Button> to try again.
              </span>
            }
            type='error'
          />
        </Flex>
      ) : dataSource.length > 0 ? (
        <Table
          loading={loading}
          size='small'
          rowKey='zohoKey'
          dataSource={dataSource}
          columns={columns}
          rowSelection={rowSelection}
          pagination={false}
          onRow={(record) => {
            return {
              onClick: () => {
                onRowClick(record);
              },
            };
          }}
        />
      ) : (
        <Flex justify='center' align='center'>
          <Empty description='No data to enrich' />
        </Flex>
      )}

      {contextHolder}
    </Modal>
  );
};

export default EnrichDataModal;
