import React, { useState } from 'react';

import EnrichDataModal from './enrich-data-modal';

const EnrichAccount: React.FC = () => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <>
      <div onClick={() => setOpen(true)}>Enrich Data</div>
      {open && (
        <EnrichDataModal open={open} onCancel={() => setOpen(false)} onOk={() => setOpen(false)} />
      )}
    </>
  );
};

export default EnrichAccount;
