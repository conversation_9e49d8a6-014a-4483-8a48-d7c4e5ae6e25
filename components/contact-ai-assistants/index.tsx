import EnrichContact from '@/components/enrich-contact';
import OutreachContact from '@/components/outreach/contact';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import React from 'react';
import MagicSVG from '~/assets/magic-colorful.svg';

const ContactAiAssistants: React.FC = () => {
  const items: ItemType[] = [
    {
      key: 'outreach',
      label: <OutreachContact />,
    },
    {
      key: 'enrich',
      label: <EnrichContact />,
    },
  ];

  return (
    <Dropdown
      menu={{
        items,
      }}
    >
      <Button
        icon={
          <img src={MagicSVG} alt='magic' width={12} style={{ color: '#fff', marginTop: '-3px' }} />
        }
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        AI Assistants
        <DownOutlined />
      </Button>
    </Dropdown>
  );
};

export default ContactAiAssistants;
