import EmailEditor from '@/components/common/email-editor';
import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import React from 'react';

interface OutreachModalProps extends ModalProps {
  onCancel: () => void;
  onOk: () => void;
}

const OutreachModal: React.FC<OutreachModalProps> = ({ open, onOk, onCancel }) => {
  return (
    <Modal
      open={open}
      destroyOnHidden={false}
      onCancel={() => onCancel?.()}
      title='Outreach'
      onOk={() => onOk?.()}
      width={1000}
      height={800}
      style={{
        maxHeight: '100vh',
      }}
    >
      <EmailEditor />
    </Modal>
  );
};

export default OutreachModal;
