import React from 'react';
import OutreachModal from './outreach-modal';

const OutreachContact: React.FC = () => {
  const [open, setOpen] = useState<boolean>(false);
  console.log('open', open);
  return (
    <>
      <div onClick={() => setOpen(true)}>Outreach</div>
      {open && (
        <OutreachModal open={open} onCancel={() => setOpen(false)} onOk={() => setOpen(false)} />
      )}
    </>
  );
};

export default OutreachContact;
