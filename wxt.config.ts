import svgr from 'vite-plugin-svgr';
import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
console.log('wxt import.meta.env.MODE', import.meta.env.MODE);
console.log('cross NODE_ENV=development', process.env.NODE_ENV);

const isProduction = process.env.NODE_ENV === 'production';
export const isDevelopment = process.env.NODE_ENV === 'development';

export default defineConfig({
  modules: ['@wxt-dev/module-react', '@wxt-dev/auto-icons', '@wxt-dev/webextension-polyfill'],
  vite: () => ({
    plugins: [svgr()],
    assetsInclude: ['**/*.md'],
    define: {
      // 确保环境变量在构建时可用
      __DEV__: JSON.stringify(isDevelopment),
      __PROD__: JSON.stringify(isProduction),
    },
  }),
  // @ts-ignore
  autoIcons: {
    // ...
  },
  manifest: {
    name: 'InHand Sales Assistant',
    description: 'InHand Sales Assistant: AI-driven insights & automation for Zoho CRM.',
    permissions: ['scripting', 'storage', 'cookies', 'activeTab', 'contextMenus'],
    host_permissions: [
      '*://crm.zoho.com/*',
      '*://crm.zoho.com.cn/*',
      '*://one.zoho.com/*',
      '*://*.zoho.com/*',
      '*://*.zoho.com.cn/*',
      // 开发环境添加本地主机权限
      ...(isDevelopment ? ['*://127.0.0.1/*', '*://localhost/*'] : []),
    ],
    default_locale: 'en',
    action: {
      default_title: 'InHand Sales Assistant',
      default_popup: 'popup/index.html',
    },
    background: {
      service_worker: 'background/background.ts',
    },
    web_accessible_resources: [
      {
        resources: ['main-world.js'],
        matches: ['*://crm.zoho.com/*', '*://crm.zoho.com.cn/*', '*://one.zoho.com/*'],
      },
    ],
    extension: {
      pages: 'pages.html',
    },
  },
  webExt: {
    disabled: true,
    openConsole: isDevelopment,
    openDevtools: isDevelopment,
  },
  imports: {
    eslintrc: {
      enabled: 9,
    },
  },
  outDir: 'dist',
});
