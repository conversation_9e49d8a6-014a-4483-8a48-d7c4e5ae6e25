import { storage } from '#imports';
import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';
import { getRequestCookies } from './storage';

// 实时获取 csrfToken
export const getRealTimeCsrfToken = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      window.removeEventListener('message', messageHandler);
      reject(new Error('Timeout'));
    }, 2000);

    const messageHandler = (event: MessageEvent) => {
      if (event.source !== window || event.data.type !== 'inhand:csrfTokenResponse') return;

      clearTimeout(timeout);
      window.removeEventListener('message', messageHandler);
      resolve(event.data.data || '');
    };

    window.addEventListener('message', messageHandler);
    window.postMessage({ type: 'inhand:getCsrfToken' }, '*');
  });
};

export const getZohoHeaderRequest = async () => {
  const requiredCookies = await getRequestCookies();

  let csrfToken: string;
  try {
    csrfToken = await getRealTimeCsrfToken();
  } catch {
    csrfToken = (await storage.getItem('local:csrfToken')) || '';
  }

  const crmZgid = await storage.getItem<string>('local:crmZgid');

  return {
    Cookie: requiredCookies?.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; '),
    'X-Zcsrf-Token': `crmcsrfparam=${csrfToken}`,
    'User-Agent': navigator.userAgent,
    'X-CRM-ORG': crmZgid ?? 663291548,
  };
};

const BASE_URL = 'https://crm.zoho.com';

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  withCredentials: false,
  validateStatus: (status) => {
    return status >= 200 && status < 500;
  },
});

axiosInstance.interceptors.request.use(async (config) => {
  config.headers['Content-Type'] = 'application/json';
  config.headers['Accept'] = 'application/json';

  config.headers['X-CRM-ORG'] =
    config.headers['X-CRM-ORG'] || (await storage.getItem('local:crmZgid')) || 663291548;
  config.headers['X-Zcsrf-Token'] =
    config.headers['X-Zcsrf-Token'] || `crmcsrfparam=${await getRealTimeCsrfToken()}`;
  return config;
});

export const zohoRequest = async (url: string, config: AxiosRequestConfig) => {
  let params: any;
  if (config && config?.params) {
    params = Object.fromEntries(Object.entries(config.params).filter(([, value]) => value !== ''));
  }

  try {
    const response = await axiosInstance(url, {
      ...config,
      params: params,
    });
    return response.data;
  } catch (error) {
    // Add better error handling
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 403) {
        throw new Error('API key is invalid or expired');
      }
      throw new Error(`Request failed: ${error.response?.data?.message || error.message}`);
    }
    throw error;
  }
};

export default zohoRequest;
