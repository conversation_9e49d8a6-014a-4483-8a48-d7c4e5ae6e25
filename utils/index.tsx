import type {
  ContentScriptContext,
  ShadowRootC<PERSON>nt<PERSON>Ui,
  ShadowRootContentScriptUiOptions,
} from '#imports';
import { createShadowRootUi, storage } from '#imports';
import { waitElement } from '@1natsu/wait-element';
import { isPromise } from 'es-toolkit';
import log from 'loglevel';
import { minimatch } from 'minimatch';

// global registry for unique UI instances
const globalUIRegistry = new Map<string, ShadowRootContentScriptUi<any>>();

export const getAccountId = () => {
  const pathname = window.location.pathname.split('/').filter(Boolean);
  return pathname[pathname.length - 1];
};

export const getAccountName = () => {
  return document.getElementById('title_ACCOUNTNAME')?.textContent?.trim();
};

// 实时获取 User info in zoho
export const getZohoUserInfo = (): Promise<{
  email: string;
  name: string;
  info?: Record<string, any>;
  [key: string]: unknown;
}> => {
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      window.removeEventListener('message', messageHandler);
      reject(new Error('Timeout'));
    }, 2000);

    const messageHandler = (event: MessageEvent) => {
      if (event.source !== window || event.data.type !== 'inhand:zohoUserInfoResponse') return;

      clearTimeout(timeout);
      window.removeEventListener('message', messageHandler);
      resolve(event.data.data || '');
    };

    window.addEventListener('message', messageHandler);
    window.postMessage({ type: 'inhand:getZohoUserInfo' }, '*');
  });
};

export const getCurrentUser = async (): Promise<{ username?: string; email?: string }> => {
  const zohoUserInfo = await getZohoUserInfo();
  let username = document.querySelector<HTMLInputElement>('#salesperson_name')?.value;
  if (!username) {
    username = document.querySelector('.profile_username')?.textContent?.trim();
  }

  const email =
    document.querySelector('crm-nextgenui-feedback')?.getAttribute('cur-user-mail') ?? undefined;
  return {
    username: username ?? zohoUserInfo?.name,
    email: email ?? zohoUserInfo?.email,
  };
};

export const isOneHost = () => {
  return window.location.ancestorOrigins?.length > 0
    ? window.location.ancestorOrigins[0]?.includes('one.zoho.com')
    : false;
};

export const getContactId = () => {
  const pathname = window.location.pathname.split('/').filter(Boolean);
  return pathname[pathname.length - 1];
};

// 获取元素中 src 的searchParams 的属性
export const getSearchParams = (element: Element, key: string) => {
  const url = element.getAttribute('src');
  if (url) {
    const urlObj = new URL(url);
    return urlObj.searchParams.get(key);
  }
  return null;
};

// 获取当前登录用户的ID
export const getUserId = async () => {
  const zohoUserID = await storage.getItem('local:zohoUserID');
  if (zohoUserID) {
    return zohoUserID;
  }
  const userProfileImg = await waitElement('#topdivuserphoto_3091799000282499001');

  if (userProfileImg) {
    const userId = getSearchParams(userProfileImg, 'ID');
    if (userId) {
      return userId;
    }
  }

  const profileMenuImg = await waitElement('.crmProfileMenu img');
  if (profileMenuImg) {
    const userId = getSearchParams(profileMenuImg, 'ID');
    if (userId) {
      return userId;
    }
  }
  const pConnectIframe = await waitElement('iframe#pconnect[name="wmspconnect"]');
  if (pConnectIframe) {
    const userId = getSearchParams(pConnectIframe, 'uname');
    if (userId) {
      return userId;
    }
  }

  const wmsIframe = await waitElement('iframe[name="wms"]');

  if (wmsIframe) {
    const userId = getSearchParams(wmsIframe, 'tabid');
    if (userId) {
      return userId;
    }
  }
  return '860897803';
};

function isReactDOMRootLike(target: unknown) {
  return (
    typeof target === 'object' &&
    target !== null &&
    'unmount' in target &&
    typeof (target as { unmount: () => void }).unmount === 'function'
  );
}

function tryUnmount(target: unknown) {
  if (typeof target === 'function') {
    target();
  } else if (isReactDOMRootLike(target)) {
    (target as { unmount: () => void }).unmount();
  } else if (isPromise(target)) {
    target.then(tryUnmount);
  }
}

export const createAutoMountShadowRootUI = async <T = any,>(
  ctx: ContentScriptContext,
  options: ShadowRootContentScriptUiOptions<T> & {
    pathMatches?: (() => boolean) | string[];
    unique?: boolean; // new option to ensure global uniqueness
  },
): Promise<ShadowRootContentScriptUi<T>> => {
  const { pathMatches, unique = false, ...rest } = options;

  // check if unique UI already exists
  if (unique && rest.name && globalUIRegistry.has(rest.name)) {
    log.debug(`returning existing unique UI: ${rest.name}`);
    return globalUIRegistry.get(rest.name)!;
  }

  const isMountable = () => {
    if (typeof pathMatches === 'function') {
      return pathMatches();
    } else if (Array.isArray(pathMatches)) {
      return pathMatches.some((match: string) => minimatch(window.location.pathname, match));
    }
    return true;
  };

  let autoMounted = false;
  const tryToMount = () => {
    const mountable = isMountable();
    if (mountable && !ui.mounted && !autoMounted) {
      ui.autoMount();
      autoMounted = true;
    } else if (!mountable && autoMounted) {
      ui.remove();
      autoMounted = false;
    }
  };

  let mutationObserver: MutationObserver | null = null;
  log.debug(`creating shadow root ui ${rest.name}, unique: ${unique}`);
  let ui: ShadowRootContentScriptUi<T> = await createShadowRootUi<T>(ctx, {
    ...rest,
    onMount: (container: HTMLElement, shadow: ShadowRoot, shadowHost: HTMLElement) => {
      if (ui.mounted) {
        // ui.remove();
        log.warn(`shadow ui ${rest.name} already mounted`);
        return ui.mounted;
      }

      log.debug(`shadow ui ${rest.name} mounted`);
      // listen for shadowHost removal event
      const handleAnchorRemoval = () => {
        log.debug(`shadow ui anchor ${rest.name} removed`);
        // unmount the shadow ui and re-auto mount it
        ui.remove();
        autoMounted = false;
        tryToMount();
      };

      mutationObserver = new MutationObserver((mutations) => {
        for (const mutation of mutations) {
          if (mutation.type === 'childList') {
            mutation.removedNodes.forEach((node) => {
              if (node === shadowHost.parentNode) {
                handleAnchorRemoval();
              }
            });
          }
        }
      });

      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return rest.onMount?.(container, shadow, shadowHost);
    },
    onRemove: (mounted: T | undefined) => {
      log.debug(`shadowRoot ${rest.name} removed`);
      mutationObserver?.disconnect();
      mutationObserver = null;

      // remove from global registry when UI is removed
      if (unique && rest.name && globalUIRegistry.has(rest.name)) {
        globalUIRegistry.delete(rest.name);
        log.debug(`removed unique UI from registry: ${rest.name}`);
      }

      tryUnmount(mounted);
      rest.onRemove?.(mounted);
    },
  });

  // register unique UI in global registry
  if (unique && rest.name) {
    globalUIRegistry.set(rest.name, ui);
  }

  setTimeout(tryToMount, 0);

  ctx.addEventListener(window, 'wxt:locationchange', tryToMount);

  return ui;
};

export const isDevelopment = process.env.NODE_ENV === 'development';
