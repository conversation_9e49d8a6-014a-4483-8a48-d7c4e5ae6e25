#!/bin/bash

# <PERSON><PERSON>t to fix SCSS syntax in CSS files
echo "Fixing SCSS syntax in CSS files..."

# Find all CSS files that contain SCSS syntax
css_files=$(find components/common/rich-editor -name "*.css" -type f)

for file in $css_files; do
    if grep -q "&" "$file"; then
        echo "Processing: $file"
        
        # Create a backup
        cp "$file" "$file.backup"
        
        # Use sed to fix common SCSS syntax issues
        # Note: This is a basic conversion, complex nesting might need manual review
        
        # Convert & selectors to proper CSS
        sed -i '' 's/  &\([^{]*\){/.\1 {/g' "$file"
        sed -i '' 's/    &\([^{]*\){/  .\1 {/g' "$file"
        
        # Fix nested selectors (basic cases)
        # This is a simplified approach - complex nesting needs manual review
        
        echo "  - Fixed SCSS syntax in: $file"
    fi
done

echo "SCSS syntax fixing complete!"
echo "Note: Complex nested selectors may need manual review."
echo "Backup files created with .backup extension."
