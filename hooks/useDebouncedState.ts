import { useDebounceFn } from 'ahooks';
import type { DebounceOptions } from 'ahooks/lib/useDebounce/debounceOptions';
import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';

/**
 * debounced state, setState will be debounced, the state will be updated after a delay
 * @param initialState initial state
 * @param options debounce options
 * @returns state and setState function
 */

export default function useDebouncedState<S>(
  initialState?: S | (() => S),
  options?: DebounceOptions,
): [S, Dispatch<SetStateAction<S>>] {
  const [state, setState] = useState<S>(initialState as any);
  const { run } = useDebounceFn(setState, {
    wait: 300,
    ...options,
  });
  return [state, run];
}
