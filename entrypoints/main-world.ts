export default defineUnlistedScript(() => {
  // 在主世界中运行的代码
  function getUserId() {
    const crmZuid = (window as any).crmZuid; // 替换为实际变量名
    if (crmZuid) {
      return crmZuid;
    }
    const zoho = (window as any).$zoho; // 替换为实际变量名
    return zoho?.salesiq?.values?.info?.zuid;
  }

  function getUserInfo() {
    const zoho = (window as any).$zoho; // 替换为实际变量名
    return zoho?.salesiq?.values;
  }

  function getCurrentCsrfToken() {
    return (window as any).csrfToken;
  }

  const csrfToken = getCurrentCsrfToken();

  const zohoUserID = getUserId();
  window.postMessage(
    {
      type: 'inhand:zohoUserID',
      data: zohoUserID,
    },
    '*',
  );
  window.postMessage(
    {
      type: 'inhand:csrfToken',
      data: csrfToken,
    },
    '*',
  );

  // 监听实时获取 host 的请求
  window.addEventListener('message', (event) => {
    if (event.source !== window) return;

    if (event.data.type === 'inhand:getCsrfToken') {
      window.postMessage(
        {
          type: 'inhand:csrfTokenResponse',
          data: getCurrentCsrfToken(),
        },
        '*',
      );
    }

    if (event.data.type === 'inhand:getZohoUserInfo') {
      const userInfo = getUserInfo();
      window.postMessage(
        {
          type: 'inhand:zohoUserInfoResponse',
          data: userInfo,
        },
        '*',
      );
    }
  });

  const getCrmZgid = () => {
    return (window as any).crmZgid;
  };

  // crmPortal
  const crmZgid = getCrmZgid();
  window.postMessage(
    {
      type: 'inhand:crmZgid',
      data: crmZgid,
    },
    '*',
  );
});
