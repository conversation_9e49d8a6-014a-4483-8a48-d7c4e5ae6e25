import * as AntdIcons from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-components';
import { ProLayout } from '@ant-design/pro-components';
import {
  createHashHistory,
  createRootRoute,
  createRouter,
  Link,
  Outlet,
  useLocation,
} from '@tanstack/react-router';
import { createElement, useMemo } from 'react';
import logo from '~/assets/logo.svg'; // 动态获取 Ant Design 图标组件
import { createRouteTree } from './config/converter.ts';
import routesConfig from './config/routes.ts'; // 动态获取 Ant Design 图标组件
import './layout.css'; // 动态获取 Ant Design 图标组件

// 动态获取 Ant Design 图标组件
const dynamicIcon = (iconName: string) => {
  const IconComponent = iconName ? AntdIcons[iconName as keyof typeof AntdIcons] : undefined;
  if (IconComponent) {
    return createElement(IconComponent as any);
  }
  return null;
};

// 转换路由配置为 ProLayout 菜单数据
const transformRouteConfigToMenuData = (routeConfigs: any[]): MenuDataItem[] => {
  // @ts-ignore
  return routeConfigs
    .filter((config) => !config.redirect) // 过滤掉重定向路由
    .map((config) => {
      const { path, name, icon, routes: childrenRoutes } = config;

      if (childrenRoutes && childrenRoutes.length > 0) {
        return {
          path,
          name,
          icon: icon ? dynamicIcon(icon) : undefined,
          routes: transformRouteConfigToMenuData(childrenRoutes),
        };
      }

      return {
        path,
        name,
        icon: icon ? dynamicIcon(icon) : undefined,
      };
    });
};

// Layout组件
const LayoutComponent = () => {
  const menuData = useMemo(() => {
    // 直接使用路由配置而不是依赖router实例
    return transformRouteConfigToMenuData(routesConfig);
  }, []);

  const { pathname } = useLocation();

  return (
    <ProLayout
      title='InHand Sales Assistant'
      logo={logo}
      menuItemRender={(item, dom) => <Link to={item.path || '/'}>{dom}</Link>}
      route={{ routes: menuData }}
      location={{ pathname }}
      layout='mix'
      fixedHeader
      fixSiderbar
      splitMenus
      colorWeak={false}
      className='sales-agent-container'
    >
      <Outlet />
    </ProLayout>
  );
};

// 正确地创建并导出一个根路由实例
export const rootRoute = createRootRoute({
  component: LayoutComponent,
});

// 转换路由配置为路由树
const routeTree = rootRoute.addChildren(createRouteTree(routesConfig, rootRoute));

const hashHistory = createHashHistory();

// 创建并导出路由实例，供其他文件使用
export const router = createRouter({
  routeTree,
  history: hashHistory, // 插件环境必须使用 'hash'
});

// 注册路由类型
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}
