.sales-agent-container .ant-menu-horizontal > .ant-menu-item-selected {
  background-color: rgb(0 0 0 / 4%);
}

.sales-agent-container .ant-pro-layout-content {
  width: 100%;
  max-height: calc(100% - 56px);
  padding-block: 0;
  padding-inline: 0;
  overflow: auto;
}

.sales-agent-container .ant-menu-light.ant-menu-horizontal > .ant-menu-item-selected,
.sales-agent-container .ant-menu-light.ant-menu-horizontal > .ant-menu-submenu-selected {
  background-color: transparent;
  font-weight: 600;
  color: #30363f;
  font-size: 16px;
}

.sales-agent-container .ant-menu-light.ant-menu-horizontal > .ant-menu-item {
  font-size: 16px;
}

.sales-agent-container
  .ant-menu-light
  .ant-menu-item:not(.ant-menu-item-selected, .ant-menu-submenu-selected):hover,
.sales-agent-container
  .ant-menu-light
  > .ant-menu
  .ant-menu-item:not(.ant-menu-item-selected, .ant-menu-submenu-selected):hover,
.sales-agent-container
  .ant-menu-light
  .ant-menu-item:not(.ant-menu-item-selected, .ant-menu-submenu-selected)
  > .ant-menu-submenu-title:hover,
.sales-agent-container
  .ant-menu-light
  > .ant-menu
  .ant-menu-item:not(.ant-menu-item-selected, .ant-menu-submenu-selected)
  > .ant-menu-submenu-title:hover {
  background: transparent;
  color: #1677ff;
}

.sales-agent-container .ant-layout-sider-children .ant-menu-light .ant-menu-item-selected {
  background-color: transparent;
  color: #1677ff;
  font-weight: 600;
}
