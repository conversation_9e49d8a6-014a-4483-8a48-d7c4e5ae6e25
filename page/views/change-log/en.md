## 2025-08-30 · v0.7.1

### Bug Fixes

- **Lookalike Companies**: Fixed issue where error messages were incorrectly displayed after task completion

## 2025-08-29 · v0.7.0

### 🔧 Feature Optimization

- **Lookalike Companies Display Optimization**: Real-time displayed company information now shows whether the company already exists in CRM
- **Lookalike Companies**: Changed Lookalike Customer and Prospecting to Lookalike Companies, more accurately reflecting the feature's meaning
- **Smart Create**: Added detailed usage instructions and case demonstrations for intelligent Account/Contact creation features

### ✨ New Features

- **Manual**: Added Manual link in the plugin for online viewing of feature introductions and detailed usage instructions
- **Changelog**: Added Changelog link in the plugin for easy viewing of update content

## 2025-08-27 · v0.6.3

### 🔧 Feature Optimization

- **Lookalike Companies Display Optimization**: Now you can view whether the found companies already exist in CRM
- **Smart Create Account**: When creating accounts intelligently finds multiple companies, you can now see if they already exist in CRM
- **Enrich Contact Enhancement**: Smart contact information completion now supports direct phone information retrieval
- **Smart Create Contact**: Smart contact creation now supports email-based search
- **Address Information Optimization**: When adding or enriching Account and Contact information, the State field in addresses now uses abbreviations

## 2025-08-22 · v0.6.2

### 🔧 Feature Optimization

- **Lookalike Customer**: Changed Prospecting to Lookalike Customer, better aligning with actual workflow
- **Lookalike Customer**: Added descriptive content and examples
- Optimized button icon sizes

## 2025-08-22 · v0.6.1

### Optimization

- **Add Company**: After completion, you can now jump to details page

### Bug Fixes

- **Smart Add Company**: Fixed compatibility issues with mismatched error types

## 2025-08-22 · v0.6.0

### ✨ New Features

- **Smart Create Contact**: Support for intelligently matching and adding corresponding contact information to CRM based on user input prompts
- **Account Information Enrichment**: New Account Enrichment feature to complete detailed information for existing accounts in CRM
- **Contact Information Enrichment Upgrade**: Expanded original phone completion feature to complete all contact information, including email, position, social media, etc.

### 🔧 Feature Optimization

- **Tracing Enhancement**: Added metadata for each AI operation tracking, recording operator information, improving audit capabilities
- **Data Retrieval Optimization**: Contact information now directly retrieved from Zoho CRM, improving data accuracy and real-time performance
- **Lookalike Companies Optimization**: Optimized similar company search process, significantly reduced search time, enhanced user experience
- **Smart Create Account Enhancement**: Now supports batch processing of multiple companies, can simultaneously search multiple target companies, improving efficiency

## 2025-08-15 · v0.5.3

### Bug Fixes

- Fixed issue where some traces were missing USER ID in langfuse trace

### Optimization

- When finding similar companies, on top of the original workflow, each found company is immediately pushed to the page for display

## 2025-08-08 · v0.5.2

### Bug Fixes

- Fixed issue where Prospecting could not create new Thread

## 2025-08-08 · v0.5.1

### Bug Fixes

- Fixed occasional persistent white screen issue in Prospecting

## 2025-08-07 · v0.5.0

### ✨ New Features

- Smart Create feature launched: Support for intelligently creating Accounts and syncing to CRM system based on input company name, website, country information.
- Task completion reminders: After task completion, automatic notification reminders will be triggered within the CRM system, improving task tracking efficiency.

### 🔧 Feature Optimization

- Prospecting workflow optimization:
  - Improved quantity and completeness of returned data;
  - Enhanced analysis capabilities for template company data, optimized similar company matching mechanism, making recommendation results more accurate;
  - Similar companies only display core basic information, effectively reducing search response time.

- Account addition popup error prompts

### Bug Fixes

- Fixed issue where after Prospecting More, it didn't stay in current conversation but returned to the first conversation.
- Fixed issue where Prospecting would continue showing Loading after first error.
