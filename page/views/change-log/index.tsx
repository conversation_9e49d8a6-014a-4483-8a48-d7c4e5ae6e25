import ProMarkdown from '@/components/common/ProMarkdown';
import { isDevelopment } from '@/utils/index';
import { Flex } from 'antd';
import React from 'react';
import en from './en.md?raw';
import zh from './zh.md?raw';

const ChangeLog: React.FC = () => {
  return (
    <Flex
      vertical
      justify={'center'}
      style={{
        maxWidth: 1400,
        overflowX: 'auto',
        margin: 'auto',
      }}
    >
      <ProMarkdown content={isDevelopment ? zh : en} hiddenSider />
    </Flex>
  );
};

export default ChangeLog;
