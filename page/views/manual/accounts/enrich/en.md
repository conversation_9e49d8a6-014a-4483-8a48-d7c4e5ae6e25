## 1. Feature Overview

The "Enrich Account Information" feature is used to automatically enhance and improve the detailed information of existing customer accounts in the CRM system. The system obtains the latest and most comprehensive business information of enterprises through the Apollo database, including contact details, industry classification, revenue scale, employee count, and other key data, helping sales teams gain a more comprehensive understanding of target customers.

---

## 2. Use Cases

- When customer account information in CRM is incomplete or outdated
- When you need to obtain the latest customer information

---

## 3. Quick Start

1. **Enter Account Details Page** Navigate to the customer account details page in the CRM system for which you need to enrich information

2. **Start Enrichment Function** Click the **Enrich Data** button on the page and wait for data updates

![enrich-account-0](./assets/enrich-account-0.png)

3. **Select Update Data** Review the enriched information and select the data fields you want to update

![enrich-account-1](./assets/enrich-account-1.png)

4. **Confirm Update** Click the **Update** button to complete the information update

![enrich-account-2](./assets/enrich-account-2.png)

5. **View Results** After the update is complete, the page will automatically refresh to display the enriched account information

---

## 4. Working Principle & Default Behavior

The system's information enrichment workflow is as follows:

- **Data Source Acquisition**: First, retrieve existing account basic information (company name, website, region) from the CRM system (Zoho)
- **Apollo Data Query**: Use an Apollo business database for deep information retrieval to obtain complete business profiles of enterprises
- **Intelligent Data Extraction**: Utilize AI models to structure raw data and extract key business information
- **Information Classification**: Automatically analyze and classify enterprise industry types, market segments, account types, etc.
- **Data Validation and Backfill**: Update the enriched information back to the CRM system

### Default Strategy:

- **Required Information Verification**: At least one valid piece of information among company name, website, or region is required
- **Data Override Rules**: Only enrich missing information, do not override existing data
- **Industry Classification**: Automatically select the most matching classification

---

## 5. Usage Flow (Detailed Steps)

1. **Access Account Details Page**

- Navigate to the target customer account details page in the CRM system
- Ensure the account has basic information (company name, website, or region information)

2. **Trigger Information Enrichment**

- Find and click the **Enrich Data** button on the account details page
- The system begins an automatic processing workflow

3. **Information Retrieval Phase**

- System displays "Retrieving enterprise information..." status prompt
- Obtain complete business profiles from an Apollo database
- This process usually takes a few seconds

4. **Data Processing Phase**

- System displays the "Analyzing and organizing information..." status prompt
- AI models extract and structure key business information

5. **Information Confirmation and Update**

- Data preview window appears, showing enriched information
- Users can select specific fields to update
- Click **Update** button to confirm updates

6. **Complete Update**

- Page automatically refreshes to display the latest account information

---

## 6. Strategy & Details

### Data Acquisition Strategy

- **Multi-parameter Query**: Prioritize using combinations of company name + region + website for queries to improve matching accuracy
- **Intelligent Search**: When direct queries fail, use AI agents for intelligent search and matching
- **Caching Mechanism**: Same queries return cached results within 24 hours, improving response speed

---

## 7. Smart Optimization Features

The system has the following intelligent processing capabilities that require no additional configuration:

### Automatic Recognition and Matching

- **Company Name Intelligent Matching**: Automatically identify different expressions of company full names, abbreviations, brand names, etc.
- **Geographic Location Parsing**: Intelligently parse geographic information such as headquarters location, branch offices, etc.
- **Business Scope Analysis**: Automatically determine main business based on company website content and descriptions

### Data Quality Assurance

1. **Accuracy Priority**: Ensure accuracy of enriched information, avoid incorrect matching
2. **Completeness Guarantee**: Obtain comprehensive enterprise information as much as possible
3. **Real-time Consideration**: Prioritize obtaining the latest enterprise dynamics and changes

---

## 8. Frequently Asked Questions (FAQ)

**Q1:** Why does enterprise information enrichment sometimes fail?  
**A1:** Possible reasons include: 1) Enterprise information does not exist or is incomplete in the database; 2) Account basic information (company name, website, region) is not accurate enough; 3) Network connection issues. It's recommended to check enterprise name spelling, ensure the website address is correct, or retry later.

**Q2:** How is the accuracy of enriched information guaranteed?  
**A2:** The system uses Apollo authoritative business database and AI intelligent analysis, with accuracy exceeding 90%. It's recommended to manually verify enriched information before important business activities to ensure key information is accurate.

**Q3:** How often can I re-enrich the same enterprise's information?  
**A3:** The system has a 24-hour caching mechanism, so the same enterprise will return the same results within 24 hours. If there are major changes in enterprise information, and you need to obtain the latest information, please contact the system administrator.

**Q4:** Will the enrichment process overwrite information I have already entered?  
**A4:** No. The system will not automatically enrich information; you need to select which fields to enrich.

**Q5:** Why do some industry classifications seem inaccurate?  
**A5:** The AI system makes analysis and judgments based on enterprise's public information. For enterprises with broad business scope, there may be classification deviations. You can manually adjust inaccurate classification information after enrichment.

**Q6:** How does the enrichment function help my sales work?  
**A6:** The enrichment function can help you: 1) Quickly understand customer enterprise scale and background; 2) Prepare more targeted sales materials; 3) Identify decision-maker levels and contact information; 4) Develop more precise sales strategies.

---
