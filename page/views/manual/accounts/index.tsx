import { Tabs } from 'antd';
import React, { lazy } from 'react';

const SmartCreate = lazy(() => import('./smart-create/index'));
const Enrich = lazy(() => import('./enrich/index'));

const LookalikeCompanies = lazy(() => import('./lookalike-companies/index'));

const Accounts: React.FC = () => {
  return (
    <Tabs
      destroyOnHidden
      items={[
        {
          key: 'lookalike-companies',
          label: 'Lookalike Companies',
          children: <LookalikeCompanies />,
        },
        {
          key: 'enrich',
          label: 'Enrich Account',
          children: <Enrich />,
        },
        {
          key: 'smart-create',
          label: 'Smart Create Account',
          children: <SmartCreate />,
        },
      ]}
    />
  );
};

export default Accounts;
