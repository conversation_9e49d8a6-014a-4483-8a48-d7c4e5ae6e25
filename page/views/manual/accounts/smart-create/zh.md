## 1. 功能概述

智能创建 Account 功能可以根据用户输入的公司名称或信息，自动搜索、识别和创建企业账户到 CRM 系统中。该功能结合了 AI 智能搜索和自动化数据处理，大大简化了销售人员添加新客户账户的工作流程。

---

## 2. 使用场景

- 当销售人员需要快速将潜在客户公司添加到 CRM 系统时
- 当获得新的线索信息，需要建立客户档案时
- 当只知道公司名称但缺少详细企业信息时

---

## 3. 快速上手

1. 进入 **Accounts** 页面，点击 **Smart Create** 按钮

   ![smart-create-0](./assets/smart-create-0.png)

2. 在弹出的输入框中填写公司信息（公司名称、官网、国家等）

   ![smart-create-1](./assets/smart-create-1.png)

3. 系统自动进行智能查找与匹配

   ![smart-create-2](./assets/smart-create-2.png)

4. 根据匹配结果选择或确认目标公司
5. 系统自动创建账户并跳转到详情页面

---

## 4. 工作原理 & 默认行为

智能创建 Account 功能采用三阶段自动化处理流程：

### 智能搜索阶段

- 系统使用 Apollo 数据库进行企业信息搜索
- 支持模糊匹配和智能识别，自动优化搜索条件
- 优先匹配公司名称，同时考虑网站域名、地理位置等信息
- 自动去除公司名称后缀（如"有限公司"、"Inc."、"Corp"等）

### 结果筛选与确认

- **唯一匹配**：系统自动继续处理，无需用户干预
- **多个匹配**：展示2-5个候选公司，用户手动选择
- **无匹配结果**：系统自动调整搜索策略重新尝试

### 自动创建与集成

- 自动获取公司详细信息：官网、LinkedIn、联系电话、成立年份等
- 智能分析分类：行业类型、客户类型、市场细分领域
- 转换为 Zoho CRM 标准格式并创建账户
- 设置默认字段：阶段（Prospect）、优先级（Low）、来源（AI Research）
- 检查重复记录，避免重复创建

---

## 5. 使用流程

### 步骤 1：启动 Smart Create

在 **Accounts** 页面点击 **Smart Create** 按钮，打开创建窗口

![smart-create-0](./assets/smart-create-0.png)

### 步骤 2：输入公司信息

在输入框中填写目标公司信息，建议提供尽可能详细的信息：

- **完整公司名称**：如 "Apple Inc."、"Microsoft Corporation"
- **公司网站**：如 "apple.com"、"microsoft.com"
- **地理位置**：国家、城市等
- **行业信息**：有助于提高匹配准确性

![smart-create-1](./assets/smart-create-1.png)

### 步骤 3：系统智能搜索

系统开始自动搜索过程，实时显示进度状态

![smart-create-2](./assets/smart-create-2.png)

### 步骤 4：处理搜索结果

根据搜索结果，系统采用不同处理方式：

**情况 A：唯一匹配结果**

- 系统自动选择匹配的公司
- 直接进入账户创建流程
- 创建成功后自动跳转到账户详情页

**情况 B：多个匹配结果**

- 展示2-5个候选公司供选择
- 每个选项显示公司名称、网站、LinkedIn等信息
- 用户手动选择最匹配的公司

![smart-create-3](./assets/smart-create-3.png)

**情况 C：无匹配结果**

- 系统提示未找到匹配公司
- 建议调整搜索条件或信息重新尝试

### 步骤 5：完成创建

- 系统自动创建账户并分配账户 ID
- 自动跳转至新创建的账户详情页面
- 可继续补充或编辑账户信息

---

## 6. 策略与细节

### 搜索优化策略

- **智能名称处理**：自动去除"有限公司"、"Inc."、"Corp"、"Ltd"等后缀
- **多维度匹配**：同时匹配公司名称、网站域名、地理位置
- **模糊搜索**：支持部分关键词和近似匹配
- **搜索重试**：首次无结果时自动调整条件重新搜索

### 数据质量保证

- **重复检测**：创建前检查 CRM 中是否已存在相同公司
- **信息验证**：自动验证公司网站、联系方式等信息有效性
- **标准化处理**：统一地址格式、行业分类等数据标准

### 智能分类系统

- **客户类型自动识别**：
  - EU (End User)：最终用户企业
  - OEM：原始设备制造商
  - SI (System Integrator)：系统集成商
  - DIS (Distributor)：分销商
  - 其他专业类型

- **行业智能分类**：
  - Energy（能源）、Industry（工业）、Commerce（商业）、ICT（信息通信）、Mobility（移动出行）、City（智慧城市）、Others（其他行业）

- **市场细分**：根据公司业务自动标记相关领域（如自动化、IoT、能源管理等）

---

## 7. 高效使用技巧

### 输入信息优化策略

为提高搜索成功率和创建质量，建议采用以下输入技巧：

- **完整信息优先**：提供公司全称、官网、所在国家/城市
- **多维度描述**：结合公司名称、网站域名、行业特征
- **官方名称**：使用公司官方注册名称，避免简称或昵称
- **英文优先**：国际公司建议使用英文名称搜索

### 常见输入场景与技巧

**场景 1：已知完整信息**

```
输入示例：Apple Inc., apple.com, 美国加州库比蒂诺
效果：匹配精度最高，通常能唯一确定目标公司
```

**场景 2：部分信息**

```
输入示例：Microsoft 微软
效果：系统会展示多个选项供确认选择
```

**场景 3：网站域名已知**

```
输入示例：tesla.com
效果：基于域名精确匹配，准确率极高
```

### 提高成功率的建议

- **避免使用**：过于简化的名称、内部代号、项目名称
- **首选英文**：跨国公司建议使用英文官方名称
- **包含位置**：同名公司较多时，添加地理位置信息
- **验证信息**：创建前确认公司信息准确性

---

## 8. 常见问题（FAQ）

**Q1:** 为什么搜索不到我要找的公司？  
**A1:** 可能原因：公司信息在数据库中不存在；2）输入名称与数据库记录不匹配；3）公司规模过小未被收录。建议尝试使用官方英文名称、网站域名，或添加地理位置信息重新搜索。

**Q2:** 系统返回多个公司选项时，如何选择正确的？  
**A2:** 重点对比以下信息：1）官网地址（最准确标志）；2）公司全称；3）地理位置；4）员工规模；5）LinkedIn 链接。建议选择信息最完整且与目标一致的选项。

**Q3:** 创建的账户信息不够完整怎么办？  
**A3:** 智能创建功能会自动填充可获取的基础信息。创建后可通过以下方式补充：1）手动编辑账户详细信息；2）使用"补全 Account 信息" 功能；3）添加自定义字段。

**Q4:** 提示"账户已存在"怎么处理？  
**A4:** 说明该公司已在 CRM 中存在。系统会显示现有账户 ID，您可以：1）直接使用现有账户；2）检查现有信息是否需要更新；3）避免重复创建造成数据混乱。

**Q5:** 如何提高搜索成功率？  
**A5:** 建议采用以下策略：1）使用完整官方名称；2）添加网站域名；3）包含地理位置；4）避免使用简称或昵称；5）优先使用英文名称。

---
