## 1. Feature Overview

The Smart Create Account feature can automatically search, identify, and create enterprise accounts in the CRM system based on user-provided company names or information. This feature combines AI intelligent search and automated data processing, greatly simplifying the workflow for sales personnel to add new customer accounts.

---

## 2. Use Cases

- When sales personnel need to quickly add potential customer companies to the CRM system
- When obtaining new lead information and need to establish customer profiles
- When only knowing company names but lacking detailed enterprise information

---

## 3. Quick Start

1. Enter the **Accounts** page and click the **Smart Create** button

   ![smart-create-0](./assets/smart-create-0.png)

2. Fill in company information (company name, website, country, etc.) in the popup input box

   ![smart-create-1](./assets/smart-create-1.png)

3. The system automatically performs intelligent search and matching

   ![smart-create-2](./assets/smart-create-2.png)

4. Select or confirm the target company based on matching results
5. The system automatically creates an account and jumps to the details page

---

## 4. Working Principle & Default Behavior

The Smart Create Account feature adopts a three-stage automated processing workflow:

### Intelligent Search Stage

- The system uses an Apollo database for enterprise information search
- Supports fuzzy matching and intelligent recognition, automatically optimizing search conditions
- Prioritizes company name matching while considering website domains, geographic locations, and other information
- Automatically removes company name suffixes (such as "Co., Ltd.", "Inc.", "Corp", etc.)

### Result Filtering and Confirmation

- **Unique Match**: System automatically continues processing without user intervention
- **Multiple Matches**: Display 2-5 candidate companies for manual user selection
- **No Match Results**: System automatically adjusts search strategy and retries

### Automatic Creation and Integration

- Automatically obtain detailed company information: website, LinkedIn, contact phone, founding year, etc.
- Intelligent analysis and classification: industry type, customer type, market segmentation
- Convert to Zoho CRM standard format and create an account
- Set default fields: Stage (Prospect), Priority (Low), Source (AI Research)
- Check duplicate records to avoid duplicate creation

---

## 5. Usage Flow

### Step 1: Start Smart Create

Click the **Smart Create** button on the **Accounts** page to open a creation window

![smart-create-0](./assets/smart-create-0.png)

### Step 2: Enter Company Information

Fill in target company information in the input box, recommend providing as detailed information as possible:

- **Complete Company Name**: such as "Apple Inc.", "Microsoft Corporation"
- **Company Website**: such as "apple.com", "microsoft.com"
- **Geographic Location**: country, city, etc.
- **Industry Information**: helps improve matching accuracy

![smart-create-1](./assets/smart-create-1.png)

### Step 3: System Intelligent Search

The system starts an automatic search process, displaying progress status in real-time

![smart-create-2](./assets/smart-create-2.png)

### Step 4: Process Search Results

Based on search results, the system adopts different processing methods:

**Case A: Unique Match Result**

- System automatically selects a matching company
- Directly enters the account creation workflow
- Automatically jumps to the account details page after successful creation

**Case B: Multiple Match Results**

- Display 2-5 candidate companies for selection
- Each option shows the company name, website, LinkedIn, and other information
- User manually selects the most matching company

![smart-create-3](./assets/smart-create-3.png)

**Case C: No Match Results**

- System prompts no matching company found
- Suggest adjusting search conditions or information and retry

### Step 5: Complete Creation

- System automatically creates an account and assigns account ID
- Automatically jump to the newly created account details page
- Can continue to supplement or edit account information

---

## 6. Strategy & Details

### Search Optimization Strategy

- **Intelligent Name Processing**: Automatically remove suffixes like "Co., Ltd.", "Inc.", "Corp", "Ltd"
- **Multi-Dimensional Matching**: Simultaneously match company name, website domain, geographic location
- **Fuzzy Search**: Support partial keywords and approximate matching
- **Search Retry**: Automatically adjust conditions and retry when no initial results

### Data Quality Assurance

- **Duplicate Detection**: Check if the same company already exists in CRM before creation
- **Information Verification**: Automatically verify the validity of company website, contact information, etc.
- **Standardized Processing**: Unify address format, industry classification, and other data standards

### Intelligent Classification System

- **Customer Type Auto-identification**:
  - EU (End User): End-user enterprises
  - OEM: Original Equipment Manufacturers
  - SI (System Integrator): System integrators
  - DIS (Distributor): Distributors
  - Other professional types

- **Industry Intelligent Classification**:
  - Energy, Industry, Commerce, ICT (Information Communication Technology), Mobility, City (Smart City), Others

- **Market Segmentation**: Automatically tag relevant fields based on company business (such as automation, IoT, energy management, etc.)

---

## 7. Efficient Usage Tips (Advanced Usage)

### Input Information Optimization Strategy

To improve the search success rate and creation quality, recommend adopting the following input techniques:

- **Complete Information Priority**: Provide company full name, website, country/city
- **Multi-Dimensional Description**: Combine company name, website domain, industry characteristics
- **Official Name**: Use company official registered name, avoid abbreviations or nicknames
- **English Priority**: For international companies, recommend using English names for search

### Common Input Scenarios and Tips

**Scenario 1: Complete Information Known**

```
Input Example: Apple Inc., apple.com, Cupertino, California, USA
Effect: Highest matching accuracy, usually can uniquely determine target company
```

**Scenario 2: Partial Information**

```
Input Example: Microsoft
Effect: System will display multiple options for confirmation selection
```

**Scenario 3: Website Domain Known**

```
Input Example: tesla.com
Effect: Precise matching based on domain, extremely high accuracy
```

### Suggestions to Improve Success Rate

- **Avoid Using**: Overly simplified names, internal codes, project names
- **Prefer English**: For multinational companies, recommend using English official names
- **Include Location**: When there are many companies with the same name, add geographic location information
- **Verify Information**: Confirm company information accuracy before creation

---

## 8. Frequently Asked Questions (FAQ)

**Q1:** Why can't I find the company I'm looking for?  
**A1:** Possible reasons: 1) Company information doesn't exist in a database; 2) Input name doesn't match database records;3) Company scale too small to be included. Recommend trying the official English name, website domain, or adding geographic location information for re-search.

**Q2:** When a system returns multiple company options, how to choose the correct one?  
**A2:** Focus on comparing the following information: 1) Website address (most accurate identifier); 2) Company full name; 3) Geographic location; 4) Employee scale; 5) LinkedIn link. Recommend choosing the option with the most complete information that matches the target.

**Q3:** What to do if created account information is incomplete?  
**A3:** Smart Create feature will automatically fill available basic information. After creation, you can supplement through: 1) Manually edit account detailed information; 2) Use the "Enrich Account Information" feature; 3) Add custom fields.

**Q4:** How to handle "Account already exists" prompt?  
**A4:** This indicates the company already exists in CRM. System will display existing account ID, you can: 1) Directly use an existing account; 2) Check if existing information needs updating; 3) Avoid duplicate creation causing data confusion.

**Q5:** How to improve the search success rate?  
**A5:** Recommend adopting the following strategies: 1) Use complete official name; 2) Add website domain; 3) Include geographic location; 4) Avoid using abbreviations or nicknames; 5) Prioritize using English names.

---
