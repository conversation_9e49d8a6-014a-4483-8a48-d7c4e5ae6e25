## 1. Feature Overview

The "Lookalike Companies" feature is based on existing successful customer accounts in CRM. Through AI intelligent analysis of customer companies' business models, industry characteristics, technical requirements, and other features, it automatically discovers similar potential target companies, helping sales teams quickly expand high-quality sales leads.

---

## 2. Use Cases

This feature is particularly suitable for the following sales work scenarios:

- **Success Case Replication**: Having successfully signed one customer, hoping to find more similar potential customers for batch development
- **Regional Market Expansion**: Need to quickly expand sales coverage in specific industries or regions, establishing sales funnels
- **Sales Strategy Reuse**: Hope to replicate successful experiences with similar customer groups based on successful case sales methods and approaches
- **Market Research Analysis**: Want to understand market capacity, competitive landscape, and potential opportunities for a specific customer type
- **Lead Quality Improvement**: Precisely filter high-match target customers from massive enterprise data to improve conversion rates

---

## 3. Quick Start

1. **Enter Account Details Page** Find the Account you want to use as a reference in the CRM system and click to enter the details page

2. **Start Lookalike Companies Feature** On the Account details page, find the **AI Assistants** dropdown menu and click **Lookalike Companies**

   ![prospecting-show](./assets/prospecting-0.png)

3. **Enter Search Requirements** In the popup dialog, enter your search requirements, for example:

- "Find similar manufacturing companies"
- "Search for European logistics companies of the same scale"

![prospecting-1](./assets/prospecting-1.png)

4. **Wait for System Analysis** After clicking search, the system will automatically analyze reference company characteristics and search for similar companies

   ![prospecting-ing](./assets/prospecting-ing.png)

5. **View Search Results** The system returns a list of similar companies, each with detailed information and similarity analysis

   ![prospecing-result](./assets/prospecing-result.png)

6. **Add to CRM** For companies of interest, click the **Add to CRM** button to add them to your CRM system

   ![prospecing-added](./assets/prospecing-added.png)

---

## 4. Working Principle & Default Behavior

The system adopts a three-stage intelligent analysis workflow:

### Stage 1: Reference Company Information Collection

- The system obtains complete company information of the current Account from Apollo database
- Includes company scale, industry classification, business keywords, geographic location, and other detailed data
- If the company information is not available in Apollo, the system will use basic company name and website information

### Stage 2: Ideal Customer Profile (ICP) Analysis

- Based on reference company characteristics, AI intelligently analyzes its business model and technical requirements
- Identifies application scenarios and customer types that best match Inhand products
- Generates customer profiles including dimensions such as industry, scale, region, technical requirements
- Determines optimal sales entry points and product recommendation strategies

### Stage 3: Similar Company Search & Filtering

- Uses Apollo enterprise database for structured search
- Combines web search to discover more candidate companies
- Performs multidimensional similarity analysis for each candidate company
- Filters out the most qualified company list based on similarity scores

### Default Strategy

- **Search Scope**: Prioritizes the same region as the reference company, returns 10 similar companies by default
- **Similarity Criteria**: Business model > Industry classification > Company scale > Geographic location
- **Exclusion Mechanism**: Automatically excludes previously searched companies to avoid duplicate recommendations
- **Quality Control**: Only returns high-quality candidate companies that pass similarity analysis verification

---

## 5. Usage Flow

### Step 1: Start Exploration Feature

1. **Select Reference Account**

- Choose an existing successful customer from the Account list or details page in CRM system as reference
- Recommend selecting representative and typical characteristic customer Accounts

![prospecting-show](./assets/prospecting-0.png)

2. **Enable Lookalike Companies Feature**

- Enter the Account details page
- Click **Lookalike Companies** in the **AI Assistants** dropdown menu

### Step 2: Configure Search Conditions

3. **Enter Search Requirements** In the dialog box, enter specific search requirements. The system supports the following types of descriptions:

- **Geographic Targeting**: "Find similar manufacturing companies in the United States"
- **Scale Matching**: "Search for European logistics companies of the same scale"
- **Requirements-oriented**: "Discover more companies that need industrial IoT solutions"

![prospecting-1](./assets/prospecting-1.png)

### Step 3: System Intelligent Analysis

4. **AI Automatic Analysis**

- System extracts key characteristics of a reference company
- Generates Ideal Customer Profile (ICP)
- Determines search strategy and matching criteria

![prospecting-ing](./assets/prospecting-ing.png)

5. **Database Search**

- Executes structured search in Apollo enterprise database
- Combines web search to discover more candidate companies
- Performs similarity scoring for candidate companies

### Step 4: View and Manage Results

6. **View Search Results**

- System returns a filtered list of similar companies
- Each company displays basic information, similarity analysis, and recommendation reasons

![prospecing-result](./assets/prospecing-result.png)

7. **Manage Search Results**

- **Add to CRM**: Click **Add to CRM** to add companies of interest to your CRM system
- **Continue Exploration**: Click **Prospecting More** to search for more companies using the same conditions
- **New Search**: Click **the New** button to start a new search task

![prospecting-more](./assets/prospecting-more.png)

![prospecting-new](./assets/prospecting-2.png)

8. **Result Status Management**

- Green icon indicates successful addition to CRM
- The red icon indicates addition failure
- Status display is only for the current operation, will reset after page refresh

![prospecting-add](./assets/prospecting-add.png)

![prospecing-added](./assets/prospecing-added.png)

---

## 6. Strategy & Details

### Search Strategy

- **Keyword Matching**: Precise matching based on business keywords of the reference company
- **Industry Segmentation**: Precise to professional subcategories, such as refining from "Manufacturing" to "Injection Molding Machine Manufacturing"
- **Geographic Strategy**: Prioritize searching in the same country or region as a reference company
- **Scale Matching**: Consider factors such as employee count, revenue scale, etc.

### Similarity Analysis Dimensions

- **Business Relevance**: Matching degree between the main business and Inhand product requirements
- **Technical Requirements**: Degree of a need for technologies like industrial IoT, data collection, remote monitoring
- **Application Scenarios**: Similarity with reference company in device connectivity, data monitoring, etc.
- **Customer Type**: Whether they belong to the same type as end users, system integrators, distributors, etc.

### Data Source Integration

- **Apollo Database**: Provides structured information of global enterprises
- **Web Search**: Supplements latest industry information and company dynamics
- **Multiple Verification**: Ensures accuracy and timeliness of recommended company information

---

## 7. Custom Prompts (Advanced Usage)

### When to Use Custom Prompts

- **Precise Targeting**: Need companies in specific subdivided industries or with special requirements
- **Geographic Restrictions**: Hope to find customers in specific countries, regions, or cities
- **Scale Filtering**: Only focuses on companies of specific size ranges
- **Technology-oriented**: Need companies with specific technical backgrounds or application scenarios

### Custom Prompt Examples

```
"Find medium-sized automotive parts manufacturers in Germany who need factory equipment connectivity and data collection solutions"

"Search for food processing companies in North America, with 100-500 employees, having cold chain monitoring needs"

"Discover smart manufacturing companies in the UK that are undergoing digital transformation projects"

"Find port logistics companies in Asia that need fleet management and location tracking solutions"
```

### Prompt Optimization Tips

- **Specify Industry**: Use accurate industry terms like "injection molding machine manufacturing" instead of " manufacturing"
- **Clear Requirements**: Specify technical needs like "device connectivity", "data monitoring"
- **Define Scope**: Specify geographic location, company scale, and other filtering conditions
- **Application Scenarios**: Describe specific use cases and technical applications

---

## 8. Frequently Asked Questions (FAQ)

**Q1:** Why are there fewer search results?  
**A1:** Possible reasons include: search conditions too strict, reference company quite unique, fewer related enterprises in a target region. Recommend relaxing geographic restrictions or adjusting industry scope.

**Q2:** How to improve search result accuracy and relevance?  
**A2:** Optimization strategies:

- **Use detailed custom prompts**: Clearly describe target customer industry characteristics, company scale, technical requirements
- **Choose high-quality reference Accounts**: Ensure reference company information is complete and representative
- **Staged search**: First use broad conditions to search, then use specific conditions to filter
- **Focus on business scenarios**: Emphasize application scenarios rather than pure industry classification

**Q3:** How is the accuracy of company information in search results guaranteed?  
**A3:** Data quality assurance:

- **Multi-source verification**: System integrates Apollo database and real-time web search
- **Regular updates**: Enterprise information regularly updated from authoritative data sources
- **AI verification**: Intelligent algorithms verify information consistency and reasonableness

**Q4:** How to avoid repeatedly contacting already developed customers?  
**A4:** Duplication avoidance mechanism:

- **Automatic exclusion**: System automatically excludes previously searched companies
- **CRM check**: Check if the company already exists in CRM before adding
- **Marking system**: Mark contacted companies with status
- **Regular organization**: Recommend regularly organizing CRM data, marking customer development status

---
