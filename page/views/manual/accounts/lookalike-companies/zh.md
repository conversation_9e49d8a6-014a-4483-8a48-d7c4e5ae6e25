## 1. 功能概述

"找类似公司"功能是基于CRM中现有的成功客户Account，通过AI智能分析客户公司的业务模式、行业特点、技术需求等特征，自动发现与之相似的潜在目标公司，帮助销售团队快速扩展高质量的销售线索。

---

## 2. 使用场景

该功能特别适合以下销售工作场景：

- **成功案例复制**：已经成功签约一家客户，希望找到更多类似的潜在客户进行批量开发
- **区域市场拓展**：需要在特定行业或地区快速扩展销售覆盖面，建立销售漏斗
- **销售策略复用**：希望基于成功案例的销售方法和话术，在相似客户群体中复制成功经验
- **市场调研分析**：想要了解某个客户类型的市场容量、竞争格局和潜在机会
- **线索质量提升**：从海量企业数据中精准筛选高匹配度的目标客户，提高转化率

---

## 3. 快速上手

1. **进入Account详情页面** 在CRM系统中找到你要以之为参考的Account，点击进入详情页面

2. **启动找类似公司功能** 在Account详情页面，找到 **AI Assistants** 下拉菜单，点击 **Lookalike Companies**

   ![prospecting-show](./assets/prospecting-0.png)

3. **输入搜索需求** 在弹出的对话框中输入你的搜索需求，例如：

- "找到类似的制造业公司"
- "寻找欧洲相同规模的物流企业"

![prospecting-1](./assets/prospecting-1.png)

4. **等待系统分析** 点击搜索后，系统将自动分析参考公司特征并搜索类似公司

   ![prospecting-ing](./assets/prospecting-ing.png)

5. **查看搜索结果** 系统返回类似公司列表，每家公司都包含详细信息和相似性分析

   ![prospecing-result](./assets/prospecing-result.png)

6. **添加到CRM** 对感兴趣的公司，点击 **Add to CRM** 按钮即可添加到你的CRM系统中

   ![prospecing-added](./assets/prospecing-added.png)

---

## 4. 工作原理 & 默认行为

系统采用三阶段智能分析流程：

### 第一阶段：参考公司信息收集

- 系统从Apollo数据库中获取当前Account的完整公司信息
- 包括公司规模、行业分类、业务关键词、地理位置等详细数据
- 如果Apollo中没有该公司信息，系统将使用基础的公司名称和网站信息

### 第二阶段：理想客户画像（ICP）分析

- 基于参考公司特征，AI智能分析其业务模式和技术需求
- 识别与映翰通产品最匹配的应用场景和客户类型
- 生成包含行业、规模、地域、技术需求等维度的客户画像
- 确定最适合的销售切入点和产品推荐策略

### 第三阶段：相似公司搜索与筛选

- 使用Apollo企业数据库进行结构化搜索
- 结合网络搜索发现更多候选公司
- 对每家候选公司进行多维度相似性分析
- 基于相似度评分筛选出最符合条件的公司列表

### 默认策略

- **搜索范围**：优先与参考公司相同地区，默认返回10家类似公司
- **相似度标准**：业务模式 > 行业分类 > 公司规模 > 地理位置
- **排除机制**：自动排除已搜索过的公司，避免重复推荐
- **质量控制**：只返回通过相似性分析验证的高质量候选公司

---

## 5. 使用流程

### 步骤一：启动探索功能

1. **选择参考Account**

- 在CRM系统的Account列表或详情页面中选择一个已有的成功客户作为参考
- 建议选择具有代表性和典型特征的客户Account

![prospecting-show](./assets/prospecting-0.png)

2. **开启找类似公司功能**

- 进入Account详情页面
- 在 **AI Assistants** 下拉菜单中点击 **Lookalike Companies**

### 步骤二：配置搜索条件

3. **输入搜索需求** 在对话框中输入具体的搜索要求，系统支持以下类型的描述：

- **地域定向**："找到美国类似的制造业公司"
- **规模匹配**："寻找欧洲相同规模的物流企业"
- **需求导向**："发现更多需要工业物联网解决方案的公司"

![prospecting-1](./assets/prospecting-1.png)

### 步骤三：系统智能分析

4. **AI自动分析**

- 系统提取参考公司的关键特征
- 生成理想客户画像（ICP）
- 确定搜索策略和匹配标准

![prospecting-ing](./assets/prospecting-ing.png)

5. **数据库搜索**

- 在Apollo企业数据库中执行结构化搜索
- 结合网络搜索发现更多候选公司
- 对候选公司进行相似性评分

### 步骤四：查看和管理结果

6. **查看搜索结果**

- 系统返回经过筛选的类似公司列表
- 每家公司显示基本信息、相似性分析和推荐理由

![prospecing-result](./assets/prospecing-result.png)

7. **管理搜索结果**

- **添加到CRM**：点击 **Add to CRM** 将感兴趣的公司添加到你的CRM系统
- **继续探索**：点击 **Prospecting More** 使用相同条件搜索更多公司
- **新建搜索**：点击 **New** 按钮开始新的搜索任务

![prospecting-more](./assets/prospecting-more.png)

![prospecting-new](./assets/prospecting-2.png)

8. **结果状态管理**

- 绿色图标表示成功添加到CRM
- 红色图标表示添加失败
- 状态显示仅针对当次操作，页面刷新后会重置

![prospecting-add](./assets/prospecting-add.png)

![prospecing-added](./assets/prospecing-added.png)

---

## 6. 策略与细节

### 搜索策略

- **关键词匹配**：基于参考公司的业务关键词进行精准匹配
- **行业细分**：精确到专业子类，如从"制造业"细化到"注塑机制造"
- **地域策略**：优先搜索参考公司所在的国家或地区
- **规模匹配**：考虑员工数量、营收规模等因素

### 相似性分析维度

- **业务相关性**：主营业务与映翰通产品需求的匹配度
- **技术需求**：对工业物联网、数据采集、远程监控等技术的需求程度
- **应用场景**：与参考公司在设备联网、数据监控等方面的相似性
- **客户类型**：是否属于终端用户、系统集成商、分销商等相同类型

### 数据源整合

- **Apollo数据库**：提供全球企业的结构化信息
- **网络搜索**：补充最新的行业信息和公司动态
- **多重验证**：确保推荐公司信息的准确性和时效性

---

## 7. 自定义提示词

### 何时使用自定义提示词

- **精确定位**：需要特定细分行业或特殊要求的公司
- **地域限制**：希望在特定国家、地区或城市寻找客户
- **规模筛选**：只关注特定规模范围的公司
- **技术导向**：需要具备特定技术背景或应用场景的公司

### 自定义提示词示例

```
"找到德国的中型汽车零部件制造商，需要工厂设备联网和数据采集解决方案"

"寻找北美地区的食品加工企业，规模在100-500人，有冷链监控需求"

"发现英国的智能制造公司，正在进行数字化转型项目"

"找到亚洲的港口物流企业，需要车队管理和位置追踪解决方案"
```

### 提示词优化技巧

- **具体化行业**：使用准确的行业术语，如"注塑机制造"而非"制造业"
- **明确需求**：说明技术需求，如"设备联网"、"数据监控"
- **限定范围**：指定地理位置、公司规模等筛选条件
- **应用场景**：描述具体的使用场景和技术应用

---

## 8. 常见问题（FAQ）

**Q1:** 为什么搜索结果较少？  
**A1:** 可能原因包括：搜索条件过于严格、参考公司比较独特、目标地区相关企业较少。建议放宽地域限制或调整行业范围。

**Q2:** 如何提高搜索结果的精准度和相关性？  
**A2:** 优化策略：

- **使用详细的自定义提示词**：明确描述目标客户的行业特征、公司规模、技术需求
- **选择高质量的参考Account**：确保参考公司信息完整且具有代表性
- **分阶段搜索**：先用宽泛条件搜索，再用具体条件筛选
- **关注业务场景**：重点描述应用场景而非单纯的行业分类

**Q3:** 搜索结果中的公司信息准确性如何保证？  
**A3:** 数据质量保障：

- **多源验证**：系统整合Apollo数据库和实时网络搜索
- **定期更新**：企业信息定期从权威数据源更新
- **AI验证**：智能算法验证信息的一致性和合理性

**Q4:** 如何避免重复联系已开发的客户？  
**A4:** 重复避免机制：

- **自动排除**：系统自动排除之前搜索过的公司
- **CRM检查**：添加前检查公司是否已存在于CRM中
- **标记系统**：对已联系的公司进行状态标记
- **定期整理**：建议定期整理CRM数据，标记客户开发状态

---
