import { Tabs } from 'antd';
import React, { lazy } from 'react';

const DetectContact = lazy(() => import('./detect/index'));

const EnrichContact = lazy(() => import('./enrich/index'));

const SmartCreate = lazy(() => import('./smart-create/index'));

const Contacts: React.FC = () => {
  return (
    <Tabs
      destroyOnHidden
      items={[
        {
          key: 'detect',
          label: 'Detect Contacts',
          children: <DetectContact />,
        },
        {
          key: 'enrich',
          label: 'Enrich Contact',
          children: <EnrichContact />,
        },
        {
          key: 'smart-create',
          label: 'Smart Create Contact',
          children: <SmartCreate />,
        },
      ]}
      tabBarStyle={{
        marginBottom: 0,
      }}
    />
  );
};

export default Contacts;
