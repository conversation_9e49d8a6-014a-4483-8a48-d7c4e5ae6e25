## 1. 功能概述

**Smart Create** 功能能够根据用户提供的基础信息（如姓名、公司、职位等），自动查找和匹配对应的联系人详细信息，并智能创建到 CRM 系统中。

该功能整合了多个数据源（LinkedIn、Apollo等），通过 AI 智能分析，尽可能在创建时将 Contact 信息填写完整。

---

## 2. 使用场景

该功能适用于以下场景：

- **获得新联系人线索**：从名片、会议、邮件等渠道获得基础联系人信息时
- **验证联系人身份**：确认联系人的真实身份、当前职位和联系方式时
- **避免重复录入**：在添加新联系人前，自动检测是否已存在相似记录
- **快速建档**：销售人员需要快速为潜在客户建立完整的CRM档案时

---

## 3. 快速上手

### 访问功能入口：

1. **在联系人列表页面**：点击 **Smart Create** 按钮

   ![smart-create-contacts-list-0](./assets/smart-create-contacts-list-0.png)

2. **在Account详情页面**：进入单个Account内的Contacts模块，点击 **Smart Create** 按钮

   ![smart-create-contacts-profile-0](./assets/smart-create-contacts-profile-0.png)

### 基本使用步骤：

1. **选择目标企业**：选择联系人所属的 **Account**（如果从 Account页面 进入则自动选定）
2. **输入联系人信息**：填写已知的联系人基础信息，如：

- 姓名：张三
- 职位：技术总监
- 公司邮箱：<EMAIL>
- LinkedIn 链接或其他已知信息

3. **点击智能创建**：系统开始自动匹配和信息扩充
4. **确认信息**：检查系统找到的详细信息是否正确
5. **保存到CRM**：确认后联系人将添加到您的 CRM 系统中

### 输入信息建议：

- **最少信息**：姓名 + 公司名称
- **推荐信息**：姓名 + 职位 + 邮箱
- **最佳信息**：姓名 + 职位 + 邮箱 + LinkedIn链接

---

## 4. 工作原理 & 默认行为

### 核心工作流程：

1. **信息解析**：AI 解析用户输入的联系人基础信息
2. **身份匹配**：在多个数据源中匹配对应的联系人档案
3. **重复检测**：自动比对 CRM 中已有联系人，避免重复添加
4. **信息扩充**：通过 LinkedIn、Apollo 等数据源获取完整信息
5. **数据验证**：AI 验证信息一致性和准确性
6. **标准化创建**：将联系人信息标准化后创建到 CRM 系统

### 默认策略：

- **匹配精度**：优先进行精确匹配，然后模糊匹配
- **数据源优先级**：LinkedIn > Apollo > 其他公开数据源
- **重复检测机制**：通过邮箱、LinkedIn URL、姓名+公司组合检测
- **信息完整性**：自动补全联系方式、社交媒体链接、职位描述
- **数据质量控制**：验证邮箱格式、电话号码有效性

---

## 5. 使用流程

### 方式一：从联系人列表创建

1. **进入联系人页面**

- 在CRM系统中进入 **Contacts** 列表页面
- 点击 **Smart Create** 按钮

![smart-create-contacts-list-0](./assets/smart-create-contacts-list-0.png)

2. **选择目标账户**

- 在弹出的对话框中选择联系人所属的Account
- 可通过搜索快速定位目标企业

![smart-create-contacts-list-1](./assets/smart-create-contacts-list-1.png)

3. **填写联系人信息**

- 输入已知的联系人基础信息
- 建议提供尽可能详细的信息以提升匹配准确率

![smart-create-contacts-list-2](./assets/smart-create-contacts-list-2.png)

4. **系统智能处理**

- 系统自动进行身份匹配和信息扩充
- 显示处理进度和状态

![smart-create-contacts-list-3](./assets/smart-create-contacts-list-3.png)

5. **确认并保存**

- 检查系统找到的完整信息
- 确认无误后保存到CRM

![smart-create-contacts-list-4](./assets/smart-create-contacts-list-4.png)

### 方式二：从Account详情页创建

1. **进入Account详情**

- 选择目标Account并进入详情页面
- 切换到 **Contacts** 模块

2. **启动智能创建**

- 点击 **Smart Create** 按钮
- Account信息已自动关联

![smart-create-contacts-profile-0](./assets/smart-create-contacts-profile-0.png)

3. **输入联系人信息**

- 填写联系人的基础信息
- 系统会基于当前Account进行匹配

![smart-create-contacts-profile-1](./assets/smart-create-contacts-profile-1.png)

---

## 6. 策略与细节

### 身份匹配策略：

#### **多维度匹配机制**

- **精确匹配**：使用邮箱、LinkedIn URL等唯一标识符
- **智能匹配**：基于姓名、公司、职位的组合匹配
- **模糊匹配**：处理姓名拼写差异、公司名称变更等情况
- **交叉验证**：通过多个数据源验证身份一致性

#### **信息质量评估**

- **数据完整度**：LinkedIn 资料完整程度、联系方式可用性
- **时效性评分**：信息更新时间、当前职位状态
- **准确性验证**：通过多数据源交叉验证信息准确性

#### **数据源整合策略**

- **LinkedIn**：个人资料、工作经历、技能背景、社交网络
- **Apollo**：联系方式、邮箱验证状态、电话号码
- **公开数据**：公司官网、新闻报道、会议发言等
- **AI 分析**：角色重要性评估、决策影响力分析

#### **重复检测机制**

- **主键匹配**：邮箱地址、LinkedIn URL的精确匹配
- **复合匹配**：姓名+公司+职位的组合匹配
- **智能判重**：处理同名、职位变更、公司更名等复杂情况
- **人工确认**：对相似度较高的记录提供人工确认选项

### **自动填充字段**

系统会智能填充以下CRM字段：

- **基础信息**：全名、职位标题、部门、直线电话等
- **联系方式**：工作邮箱、手机号码、办公地址等
- **社交媒体**：LinkedIn、Twitter、Facebook等社交链接
- **公司关联**：Account关联、汇报关系、团队信息等
- **扩展信息**：教育背景、技能标签、行业经验等

---

## 7. 高级使用技巧

### 输入信息优化策略：

#### **提升匹配准确率的方法**

- **多维度信息**：提供姓名、职位、邮箱、LinkedIn等多种信息
- **标准化格式**：使用标准的职位名称和公司全称
- **最新信息**：确保提供的信息是最新的，避免使用过期数据
- **英文补充**：对于国际化公司，可补充英文姓名和职位

#### **处理特殊情况**

```
情况1：同名联系人
输入：张伟 + 具体公司名称 + 部门信息

情况2：职位变更
输入：当前职位 + LinkedIn 链接确认身份

情况3：公司更名
输入：当前公司名称 + 原公司名称作为备注

情况4：中英文姓名
输入：中文姓名 + 英文姓名 + 邮箱验证
```

---

## 8. 常见问题（FAQ）

**Q1: 为什么找不到匹配的联系人信息？**  
**A1:** 可能的原因包括：

- 提供的信息过少或不准确（建议至少提供姓名+公司+职位）
- 联系人在公开数据源中的信息有限
- 姓名拼写或公司名称与实际不一致  
  **建议**：尝试提供更多维度的信息，如邮箱、LinkedIn链接等

**Q2: 如何提高信息匹配的准确率？**  
**A2:**

- 提供准确完整的基础信息（姓名、职位、公司、邮箱）
- 使用标准化的职位名称和公司全称
- 补充 LinkedIn 链接作为身份验证
- 确保信息是最新的，避免使用过期数据

**Q3: 创建的联系人信息可靠吗？**  
**A3:**

- 系统从LinkedIn、Apollo等权威数据源获取信息
- 使用AI进行多数据源交叉验证
- 建议在首次联系前核实关键信息（邮箱、电话）
- 可通过"联系人信息扩充"功能定期更新信息

**Q4: 如何处理信息不完整的情况？**  
**A4:**

- 系统会尽可能补全可获得的信息
- 对于无法获取的字段会保持空白，避免错误信息
- 可后续使用"联系人信息扩充"功能补充完善
- 建议定期维护和更新联系人数据质量

---
