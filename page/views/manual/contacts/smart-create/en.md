## 1. Feature Overview

The **Smart Create** feature can automatically search for and match corresponding detailed contact information based on basic information provided by users (such as name, company, position, etc.), and intelligently create contacts in the CRM system.

This feature integrates multiple data sources (LinkedIn, Apollo, etc.) and uses AI intelligent analysis to fill in Contact information as completely as possible during creation.

---

## 2. Use Cases

This feature is suitable for the following scenarios:

- **Obtaining new contact leads**: When obtaining basic contact information from business cards, meetings, emails and other channels
- **Verifying contact identity**: Confirming contact's real identity, current position and contact information
- **Avoiding duplicate entries**: Automatically detecting whether similar records already exist before adding new contacts
- **Quick filing**: When sales personnel need to quickly establish complete CRM profiles for potential customers

---

## 3. Quick Start

### Access Feature Entry:

1. **On the contact list page**: Click the **Smart Create** button

   ![smart-create-contacts-list-0](./assets/smart-create-contacts-list-0.png)

2. **On Account detail page**: Enter the Contacts module within a single Account, click the **Smart Create** button

   ![smart-create-contacts-profile-0](./assets/smart-create-contacts-profile-0.png)

### Basic Usage Steps:

1. **Select Target Company**: Choose the **Account** the contact belongs to (automatically selected if entering from Account page)
2. **Input Contact Information**: Fill in known basic contact information, such as:

- Name: John Smith
- Position: Technical Director
- Company Email: <EMAIL>
- LinkedIn link or other known information

3. **Click Smart Create**: System begins automatic matching and information enrichment
4. **Confirm Information**: Check if the detailed information found by the system is correct
5. **Save to CRM**: After confirmation, the contact will be added to your CRM system

### Input Information Suggestions:

- **Minimum Information**: Name + Company Name
- **Recommended Information**: Name + Position + Email
- **Optimal Information**: Name + Position + Email + LinkedIn Link

---

## 4. How It Works & Default Behavior

### Core Workflow:

1. **Information Parsing**: AI parses basic contact information input by users
2. **Identity Matching**: Match corresponding contact profiles in multiple data sources
3. **Duplicate Detection**: Automatically compare with existing contacts in CRM to avoid duplicate additions
4. **Information Enrichment**: Obtain complete information through LinkedIn, Apollo and other data sources
5. **Data Validation**: AI validates information consistency and accuracy
6. **Standardized Creation**: Standardize contact information and create in CRM system

### Default Strategy:

- **Matching Precision**: Prioritize exact matching, then fuzzy matching
- **Data Source Priority**: LinkedIn > Apollo > Other public data sources
- **Duplicate Detection Mechanism**: Detect through email, LinkedIn URL, name+company combination
- **Information Completeness**: Automatically complete contact information, social media links, position descriptions
- **Data Quality Control**: Validate email format, phone number validity

---

## 5. Usage Process

### Method 1: Create from Contact List

1. **Enter Contact Page**

- Enter the **Contacts** list page in the CRM system
- Click the **Smart Create** button

![smart-create-contacts-list-0](./assets/smart-create-contacts-list-0.png)

2. **Select Target Account**

- Select the Account the contact belongs to in the pop-up dialog
- Quickly locate the target company through search

![smart-create-contacts-list-1](./assets/smart-create-contacts-list-1.png)

3. **Fill Contact Information**

- Input known basic contact information
- Recommend providing as detailed information as possible to improve matching accuracy

![smart-create-contacts-list-2](./assets/smart-create-contacts-list-2.png)

4. **System Intelligent Processing**

- System automatically performs identity matching and information enrichment
- Display processing progress and status

![smart-create-contacts-list-3](./assets/smart-create-contacts-list-3.png)

5. **Confirm and Save**

- Check the complete information found by the system
- Save to CRM after confirming accuracy

![smart-create-contacts-list-4](./assets/smart-create-contacts-list-4.png)

### Method 2: Create from the Account Detail Page

1. **Enter Account Details**

- Select target Account and enter the detail page
- Switch to **Contacts** module

2. **Launch Smart Create**

- Click the **Smart Create** button
- Account information is automatically associated

![smart-create-contacts-profile-0](./assets/smart-create-contacts-profile-0.png)

3. **Input Contact Information**

- Fill in basic contact information
- System will match based on current Account

![smart-create-contacts-profile-1](./assets/smart-create-contacts-profile-1.png)

---

## 6. Strategy & Details

### Identity Matching Strategy:

#### **Multi-dimensional Matching Mechanism**

- **Exact Matching**: Use unique identifiers like email, LinkedIn URL
- **Intelligent Matching**: Combined matching based on name, company, position
- **Fuzzy Matching**: Handle name spelling differences, company name changes, etc.
- **Cross Validation**: Verify identity consistency through multiple data sources

#### **Information Quality Assessment**

- **Data Completeness**: LinkedIn profile completeness, contact information availability
- **Timeliness Scoring**: Information update time, current position status
- **Accuracy Verification**: Cross-validate information accuracy through multiple data sources

#### **Data Source Integration Strategy**

- **LinkedIn**: Personal profiles, work experience, skill background, social networks
- **Apollo**: Contact information, email verification status, phone numbers
- **Public Data**: Company websites, news reports, conference speeches, etc.
- **AI Analysis**: Role importance assessment, decision-making influence analysis

#### **Duplicate Detection Mechanism**

- **Primary Key Matching**: Exact matching of email addresses, LinkedIn URLs
- **Composite Matching**: Combined matching of name+company+position
- **Intelligent Deduplication**: Handle complex situations like same names, position changes, company renaming
- **Manual Confirmation**: Provide manual confirmation options for records with high similarity

### **Auto-fill Fields**

The system will intelligently fill the following CRM fields:

- **Basic Information**: Full name, position title, department, direct phone, etc.
- **Contact Information**: Work email, mobile number, office address, etc.
- **Social Media**: LinkedIn, Twitter, Facebook and other social links
- **Company Association**: Account association, reporting relationships, team information, etc.
- **Extended Information**: Educational background, skill tags, industry experience, etc.

---

## 7. Advanced Usage Tips

### Input Information Optimization Strategy:

#### **Methods to Improve Matching Accuracy**

- **Multi-Dimensional Information**: Provide multiple types of information like name, position, email, LinkedIn
- **Standardized Format**: Use standard position names and complete company names
- **Latest Information**: Ensure provided information is up to date, avoid using outdated data
- **English Supplement**: For international companies, supplement English names and positions

#### **Handling Special Situations**

```
Situation 1: Contacts with Same Name
Input: John Smith + Specific Company Name + Department Information

Situation 2: Position Change
Input: Current Position + LinkedIn Link to Confirm Identity

Situation 3: Company Renaming
Input: Current Company Name + Former Company Name as Note

Situation 4: Chinese and English Names
Input: Chinese Name + English Name + Email Verification
```

---

## 8. Frequently Asked Questions (FAQ)

**Q1: Why can't matching contact information be found sometimes?**  
**A1:** Possible reasons include:

- Insufficient or inaccurate information provided (recommend providing at least name+company+position)
- Contact has limited information in public data sources
- Name spelling or company name inconsistent with actual information  
  **Suggestion**: Try providing more dimensional information, such as email, LinkedIn links, etc.

**Q2: How to improve information matching accuracy?**  
**A2:**

- Provide accurate and complete basic information (name, position, company, email)
- Use standardized position names and complete company names
- Supplement LinkedIn links as identity verification
- Ensure information is up to date, avoid using outdated data

**Q3: Is the created contact information reliable?**  
**A3:**

- System obtains information from authoritative data sources like LinkedIn, Apollo
- Uses AI for multi-data source cross-validation
- Recommend verifying key information (email, phone) before first contact
- Can use the "Contact Information Enrichment" feature to regularly update information

**Q4: How to handle incomplete information situations?**  
**A4:**

- System will complete as much obtainable information as possible
- Fields that cannot be obtained will remain blank to avoid incorrect information
- Can subsequently use the "Contact Information Enrichment" feature to supplement and improve
- Recommend regularly maintaining and updating contact data quality

---
