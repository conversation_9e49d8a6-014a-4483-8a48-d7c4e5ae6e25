## 1. 功能概述

联系人探查功能帮助销售团队快速找到目标企业中最有价值的联系人，自动获取关键决策者信息并自动添加到 CRM 系统。

**主要价值：**

- 智能分析目标企业，自动找出最适合的联系人
- 省去手动搜索时间，提高销售效率
- 基于映翰通产品特点，精准匹配潜在客户
- 自动添加到 Zoho CRM ，避免重复录入

---

## 2. 使用场景

该功能在以下情况下使用：

- 当销售团队需要开拓新的企业客户时
- 当已知目标企业但缺少具体联系人信息时
- 当需要在大型企业中精准定位决策者或影响者时
- 当需要避免重复导入已有联系人时
- 当需要基于特定需求（如技术背景、职位级别）筛选联系人时

---

## 3. 快速上手

### 快速操作指引

1. **登录CRM** → 进入 Accounts 列表
2. **选择企业** → 点击 Detect Contacts
3. **选择模式** → Standard Detect 或 Custom AI Prompt
4. **等待完成** → 2-5分钟自动处理
5. **查看结果** → 联系人自动添加到CRM

### 客户端界面元素说明

- **Detect Contacts按钮**：启动探查功能的主要入口
- **Use Standard Detect**：使用默认搜索策略
- **Use Custom AI Prompt**：输入自定义搜索需求
- **状态指示器**：Pending(等待) → Processing(进行中) → Completed(完成)
- **通知提醒**：探查完成后的弹窗通知

---

## 4. 工作原理 & 默认行为

### 系统如何为您工作

AI 助手会按照以下步骤为您寻找最佳联系人：

1. **分析目标企业** - 从您的 CRM 中获取企业基本信息，了解企业背景
2. **智能搜索联系人** - 在全球商业数据库中寻找该企业的员工信息
3. **精准筛选** - 基于映翰通产品特点，筛选出最有价值的联系人
4. **自动录入 CRM** - 将筛选后的联系人自动添加到您的 Zoho CRM 中

### 默认搜索策略

系统会优先为您寻找这些类型的联系人：

- **产品/项目负责人** - 最有可能采购我们产品的决策者
- **企业高管（C-Level）** - 具有最终决策权的管理层
- **技术/方案负责人** - 了解技术需求的专业人士
- **采购/业务发展** - 负责供应商选择的关键角色

**智能特性：**

- 自动避免重复添加已有联系人
- 优先展示活跃度高的 LinkedIn 用户
- 根据企业规模调整搜索策略
- 默认返回 5 个精选联系人

---

## 5. 使用流程

### 方式一：单个企业详情内探查

1. **进入企业详情**

- 从 Accounts 列表进入单个企业的详情页面
- 导航到 **Contacts** 模块

![account-profile-detect](assets/account-profile-detect.png)

2. **查看历史记录**

- 鼠标悬停在 **Detect Contacts** 按钮上
- 可以看到上一次探查的过程以及结果

![account-profile-show-detect](assets/account-profile-show-detect.png)

3. **启动新的探查**

- 点击 **Detect Contacts** 按钮
- 选择探查模式（标准 or 自定义）
- 系统开始实时处理并显示进度

![account-profile-detecting](assets/account-profile-detecting.png)

4. **查看结果**

- 整个过程通常需要2-5分钟
- 完成后会收到通知提醒
- 联系人自动添加到企业的 Contacts 列表中
- 每个联系人都包含 AI 生成的推荐理由，以 Notes 的形式，添加到了系统

![account-profile-detect-notification](assets/account-profile-detect-notification.png)

### 方式二：批量探查联系人

1. **选择目标企业**

- 进入 Zoho CRM 的 **Accounts** 列表页面
- 选择您需要探查联系人的目标企业账户
- 确保企业信息（名称、网站、地址等）完整准确

![account-batch-detect](assets/account-batch-detect.png)

2. **启动探查功能**

- 点击 **Detect Contacts** 按钮
- 选择探查模式：
  - **Use Standard Detect**：使用系统预设的标准探查策略
  - **Use Custom AI Prompt**：添加您的自定义搜索需求

![account-batc-detect-custom](assets/account-batc-detect-custom.png)

3. **设置自定义条件（可选）**

- 如果选择自定义模式，在输入框中描述您的具体需求，例如：
  - "找技术部门负责人，要求有物联网项目经验"

4. **监控探查进度**

- **状态显示**：Pending（排队中）→ Processing（进行中）→ Completed（完成）
- **状态图标**：灰色表示正常，黄色表示有问题
- **进度详情**：鼠标悬停可查看详细进度和时间线

![batch-detect-status-list](assets/batch-detect-status-list.png)

![batch-detect-status-proccessing](assets/batch-detect-status-proccessing.png)

5. **查看结果**

- 探查完成后会收到通知提醒
- 联系人自动添加到该企业的 Contacts 列表中
- 每个联系人都包含 AI 生成的推荐理由，以 Notes 的形式，添加到了系统

![account-profile-detect-notification](assets/account-profile-detect-notification.png)

### 详细工作原理解析

#### 探查流程详解

**阶段1：企业背景研究**

- **数据收集**：从您的 CRM 获取企业基本信息（名称、官网、地址、行业）
- **信息补全**：通过企业官网、LinkedIn 公司页面补充详细信息
- **规模评估**：确定企业员工规模（影响后续搜索策略）
- **行业分析**：识别企业主营业务与映翰通产品的契合点

**阶段2：现有数据清理**

- **联系人查重**：扫描您 CRM 中该企业的所有现有联系人
- **LinkedIn URL提取**：收集已有联系人的 LinkedIn 档案链接
- **数据去重准备**：建立排除清单，避免重复添加
- **质量评估**：分析现有联系人的完整度和活跃度

**阶段3：智能搜索策略制定**

- **目标画像构建**：基于映翰通产品特点，定义理想联系人画像
- **搜索参数优化**：根据企业规模和行业特点调整搜索条件
- **关键词策略**：
  - 产品相关：IoT、Edge Computing、Industrial Router、Gateway
  - 职位相关：Product Manager、Technical Director、Procurement
  - 行业相关：Manufacturing、Energy、Transportation

**阶段4：多轮智能搜索**

- **初始搜索**：使用核心关键词在全球商业数据库中搜索
- **动态调整**：
  - 结果太少（<50人）：放宽职位要求 → 降低资历要求 → 扩大地域范围
  - 结果太多（>100人）：提高职位级别 → 细化技术要求 → 限定地域
- **多维度匹配**：职位+部门+资历+地域的组合搜索
- **智能过滤**：排除明显不相关的职位（如HR、财务等非目标角色）

**阶段5：AI 智能评估与排序**

- **相关性评分**：
  - 职位匹配度（40%）：技术决策者 > 业务决策者 > 采购决策者
  - 影响力评估（30%）：C-Level > VP > Director > Manager
  - 行业经验（20%）：IoT/工业自动化经验 > 一般技术经验
  - 活跃度评分（10%）：LinkedIn更新频率、资料完整度
- **购买决策权评估**：
  - 最终决策者：CEO、CTO、技术VP
  - 重要影响者：产品经理、项目经理、技术总监
  - 参与者：采购经理、业务发展经理
- **LinkedIn质量验证**：
  - 档案完整度检查
  - 最近活跃时间验证
  - 当前就职状态确认

**阶段6：数据整理与 CRM 同步**

- **信息标准化**：统一联系人数据格式
- **推荐理由生成**：为每个联系人生成个性化的联系建议
- **优先级标注**：highest > high > medium > low
- **字段映射**：将外部数据映射到Zoho CRM字段
- **批量导入**：自动创建联系人记录并关联到企业

#### 默认搜索策略

**核心搜索逻辑**

```
IF 企业规模 > 1000人 THEN
    重点搜索：部门负责人、项目经理
    关键词：Product Manager, Project Manager, Technical Director
ELSE IF 企业规模 100-1000人 THEN
    重点搜索：技术负责人、业务负责人
    关键词：CTO, VP of Engineering, Business Development
ELSE 企业规模 < 100人 THEN
    重点搜索：高管层、创始人
    关键词：CEO, CTO, Founder, Co-founder
```

**行业适配策略**

- **制造业**：优先搜索生产技术、自动化、质量管理相关职位
- **能源行业**：重点关注智能电网、配电自动化、能源管理
- **交通运输**：聚焦车联网、智能交通、物流管理
- **零售行业**：关注智能零售、供应链、数字化转型

### 幕后工作流程（您无需操作）

当您点击开始后，AI助手会自动完成以下工作：

1. **研究目标企业**

- 获取企业详细信息：规模、行业、网站等
- 了解企业背景，为搜索做准备

2. **检查现有联系人**

- 查看您CRM中该企业的现有联系人
- 确保不会重复添加

3. **AI智能搜索**

- 在全球商业数据库中搜索该企业员工
- 基于我们的产品特点制定搜索策略
- 通常会找到50-100个候选人

4. **精准筛选**

- 分析每个候选人的职位、经验、LinkedIn活跃度
- 评估他们对我们产品的潜在需求
- 筛选出5-20个最有价值的联系人

5. **智能整理**

- 自动格式化联系人信息
- 生成推荐理由
- 准备CRM录入格式

6. **自动录入CRM**

- 将筛选后的联系人添加到您的Zoho CRM
- 关联到对应企业
- 添加AI生成的联系理由备注

---

## 6. 策略与细节

### AI是如何为您选择联系人的？

**企业类型分析**

- **大型企业（100人以上）**：重点寻找部门负责人、项目经理等中层决策者
- **小型企业（100人以下）**：直接定位CEO、CTO等高层管理者
- **渠道伙伴企业**：优先产品经理、销售总监等业务核心人员
- **最终用户企业**：重点技术负责人、采购决策者

**联系人价值评估**

AI会根据以下标准为每个联系人打分：

- **最高优先级** - 产品/项目负责人：直接负责技术选型和采购
- **高优先级** - C-Level高管：最终决策权，影响大
- **中优先级** - 技术/方案专家：技术评估和建议权
- **一般优先级** - 采购/业务人员：参与采购流程

**质量保证机制**

系统如何确保推荐质量：

- **职位匹配度检查** - 确保职位与我们产品相关
- **LinkedIn 活跃度验证** - 优先推荐资料完整、更新及时的联系人
- **企业确认** - 验证联系人确实在目标企业工作
- **自动去重** - 不会推荐您CRM中已有的联系人

**智能调节功能**

系统会根据搜索结果自动调整：

- **找到联系人太少**：自动放宽搜索条件，包含更多职位类型
- **找到联系人太多**：自动收紧条件，只保留最核心的决策者
- **平衡结果**：通常为您提供5-20个精选联系人，确保质量和数量的平衡

---

## 7. 自定义搜索需求

### 默认探查 vs 自定义探查对比

| 对比维度     | 默认探查           | 自定义探查           |
| ------------ | ------------------ | -------------------- |
| **适用场景** | 常规业务开发       | 特殊项目、特定需求   |
| **搜索范围** | 全职能覆盖         | 精准定向             |
| **结果数量** | 5-20个综合性联系人 | 3-15个高度匹配联系人 |
| **匹配精度** | 85%相关性          | 95%相关性            |
| **处理时间** | 2-3分钟            | 3-5分钟              |

### 什么情况下使用自定义搜索？

#### 场景1：特定项目需求

```
当前项目：智能工厂数字化改造
自定义需求："找制造业数字化转型负责人，有智能工厂或工业4.0项目经验"
```

#### 场景2：行业专项突破

```
重点行业：新能源
自定义需求："找新能源行业的项目经理，负责充电桩、储能或智能电网项目"
```

### 自定义搜索实用模板

#### 制造业模板

```
基础版本：
"找制造业的技术负责人，负责生产线自动化或设备联网"

进阶版本：
"找汽车制造业的数字化转型经理，有MES系统或工业物联网平台项目经验，优先考虑德国和日本地区"
```

#### 交通物流模板

```
基础版本：
"找交通运输行业的IT负责人，有车队管理或物流信息化经验"

进阶版本：
"找大型物流企业的数字化负责人，有仓储自动化或车联网平台建设经验，重点关注亚太地区"
```

### AI理解您的自定义需求

**AI能够识别的关键要素：**

1. **职位层级**：CEO/CTO/VP/总监/经理/专员
2. **技术领域**：IoT/边缘计算/工业自动化/数字化转型
3. **行业背景**：制造/能源/交通/零售/金融/医疗
4. **地理区域**：亚太/欧洲/北美/具体国家/城市
5. **项目经验**：具体技术栈、解决方案、项目类型
6. **企业规模**：跨国企业/大型企业/中小企业
7. **决策权限**：最终决策者/重要影响者/技术评估者

**智能语义理解示例：**

```
输入："找负责工厂数字化的技术专家"
AI理解：
- 职位：技术总监、数字化经理、IT主管
- 行业：制造业
- 技术：工业4.0、智能制造、MES系统
- 决策权：技术选型影响者
```

### 高级自定义技巧

#### 技巧 1：排除条件

```
"找技术负责人，要求有物联网经验，但不要传统IT运维背景的"
```

#### 技巧 2：优先级设定

```
"优先找 CTO 和 技术VP，其次考虑产品经理和项目经理"
```

#### 技巧 3：业务场景描述

```
"我们要推广边缘计算网关产品，找能够评估和采购此类设备的决策者"
```

### 自定义搜索注意事项

**推荐做法：**

- 描述具体业务场景和需求
- 提供清晰的职位和行业要求
- 合理设置地域和企业规模范围
- 强调相关项目经验

**避免事项：**

- 过于宽泛的描述（如"找所有技术人员"）
- 相互矛盾的条件（如"入门级的CTO"）
- 过于狭窄的限制（如"只要2019年的项目经验"）
- 包含敏感或歧视性要求

---

## 8. 常见问题解答（FAQ）

### 为什么有时找不到合适的联系人？

**可能的原因：**

- 该企业在全球数据库中信息较少（特别是小型企业）
- 企业所在地区或行业数据覆盖有限
- 企业员工LinkedIn资料普遍不完整

**解决办法：**

- 尝试使用自定义搜索，放宽筛选条件
- 检查企业名称是否正确，有时换个企业名称写法会有不同结果

### 为什么返回的联系人数量比较少？

这是正常的！系统优先质量而非数量：

- 自动排除了您 CRM 中已有的联系人
- 只推荐与映翰通产品最相关的联系人
- 筛选掉了 LinkedIn 资料不完整的候选人
- 优先推荐决策影响力高的关键人员

**一般来说5-15个精选联系人比50个低质量联系人更有价值。**

### 搜索需要多长时间？

**正常情况下：**

- 小型企业（<100人）：1-2分钟
- 中型企业（100-1000人）：2-3分钟
- 大型企业（>1000人）：3-5分钟

### 联系人信息有错误怎么办？

- **轻微错误**：直接在 CRM 中手动修正即可
- **严重错误**：联系我们技术支持，我们会改进数据质量
- **联系人已离职**：这种情况较少，因为系统会验证 LinkedIn 活跃度

### 如何提高搜索效果？

1. **确保企业信息准确**：CRM 中的企业名称、网站等信息要准确
2. **合理使用自定义搜索**：描述具体需求，不要太泛泛而谈
3. **定期清理CRM**：移除无效联系人，提高去重效果
4. **选择合适时机**：避开企业重组、更名等特殊时期

---
