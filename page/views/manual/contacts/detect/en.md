## 1. Feature Overview

The contact detection feature helps sales teams quickly find the most valuable contacts within target companies, automatically retrieve key decision-maker information, and automatically add them to the CRM system.

**Key Value:**

- Intelligently analyze target companies and automatically find the most suitable contacts
- Save manual search time and improve sales efficiency
- Precisely match potential customers based on InHand Networks product characteristics
- Automatically add to Zoho CRM, avoiding duplicate data entry

---

## 2. Use Cases

This feature is used in the following situations:

- When sales teams need to develop new enterprise customers
- When target companies are known but lack specific contact information
- When precise targeting of decision-makers or influencers in large enterprises is needed
- When avoiding duplicate import of existing contacts is required
- When contacts need to be filtered based on specific requirements (such as technical background, position level)

---

## 3. Quick Start

### Quick Operation Guide

1. **Login to CRM** → Enter Accounts list
2. **Select Company** → Click Detect Contacts
3. **Choose Mode** → Standard Detect or Custom AI Prompt
4. **Wait for Completion** → 2-5 minutes automatic processing
5. **View Results** → Contacts automatically added to CRM

### Client Interface Elements

- **Detect Contacts Button**: Main entry point to launch the detection feature
- **Use Standard Detect**: Use the default search strategy
- **Use Custom AI Prompt**: Input custom search requirements
- **Status Indicator**: Pending → Processing → Completed
- **Notification Alert**: Pop-up notification after detection completion

---

## 4. How It Works & Default Behavior

### How the System Works for You

The AI assistant will find the best contacts for you following these steps:

1. **Analyze Target Company** - Retrieve basic company information from your CRM to understand the company background
2. **Intelligent Contact Search** - Search for employee information of the company in global business databases
3. **Precise Filtering** - Filter out the most valuable contacts based on InHand Networks product characteristics
4. **Auto-import to CRM** - Automatically add filtered contacts to your Zoho CRM

### Default Search Strategy

The system will prioritize finding these types of contacts for you:

- **Product/Project Leaders** - Decision-makers most likely to purchase our products
- **Corporate Executives (C-Level)** - Management with final decision-making authority
- **Technical/Solution Leaders** - Professionals who understand technical requirements
- **Procurement/Business Development** - Key roles responsible for supplier selection

**Intelligent Features:**

- Automatically avoid adding duplicate existing contacts
- Prioritize LinkedIn users with high activity levels
- Adjust search strategies based on company size
- Default return of 5 selected contacts

---

## 5. Usage Process

### Method 1: Detection within Individual Company Details

1. **Enter Company Details**

- Enter the individual company detail page from the Accounts list
- Navigate to the **Contacts** module

![account-profile-detect](assets/account-profile-detect.png)

2. **View History**

- Hover over the **Detect Contacts** button
- View the process and results of the last detection

![account-profile-show-detect](assets/account-profile-show-detect.png)

3. **Start New Detection**

- Click the **Detect Contacts** button
- Select detection mode (Standard or Custom)
- System begins real-time processing and displays progress

![account-profile-detecting](assets/account-profile-detecting.png)

4. **View Results**

- The entire process usually takes 2-5 minutes
- Notification reminder upon completion
- Contacts are automatically added to the company's Contacts list
- Each contact includes AI-generated recommendation reasons, added to the system as Notes

![account-profile-detect-notification](assets/account-profile-detect-notification.png)

### Method 2: Batch Contact Detection

1. **Select Target Companies**

- Enter the **Accounts** list page in Zoho CRM
- Select the target company accounts where you need to detect contacts
- Ensure company information (name, website, address, etc.) is complete and accurate

![account-batch-detect](assets/account-batch-detect.png)

2. **Launch Detection Feature**

- Click the **Detect Contacts** button
- Select detection mode:
  - **Use Standard Detect**: Use system preset standard detection strategy
  - **Use Custom AI Prompt**: Add your custom search requirements

![account-batc-detect-custom](assets/account-batc-detect-custom.png)

3. **Set Custom Conditions (Optional)**

- If choosing a custom mode, describe your specific requirements in the input box, for example:
  - "Find technical department leaders with IoT project experience"

4. **Monitor Detection Progress**

- **Status Display**: Pending → Processing → Completed
- **Status Icons**: Gray indicates normal, yellow indicates issues
- **Progress Details**: Hover to view detailed progress and timeline

![batch-detect-status-list](assets/batch-detect-status-list.png)

![batch-detect-status-proccessing](assets/batch-detect-status-proccessing.png)

5. **View Results**

- Notification reminder upon detection completion
- Contacts are automatically added to the company's Contacts list
- Each contact includes AI-generated recommendation reasons, added to the system as Notes

![account-profile-detect-notification](assets/account-profile-detect-notification.png)

### Detailed Working Principle Analysis

#### Detection Process Breakdown

**Phase 1: Company Background Research**

- **Data Collection**: Retrieve basic company information from your CRM (name, website, address, industry)
- **Information Completion**: Supplement detailed information through company website, LinkedIn company page
- **Scale Assessment**: Determine company employee scale (affects subsequent search strategy)
- **Industry Analysis**: Identify alignment between a company's main business and InHand Networks products

**Phase 2: Existing Data Cleanup**

- **Contact Deduplication**: Scan all existing contacts for the company in your CRM
- **LinkedIn URL Extraction**: Collect LinkedIn profile links of existing contacts
- **Deduplication Preparation**: Establish an exclusion list to avoid duplicate additions
- **Quality Assessment**: Analyze completeness and activity level of existing contacts

**Phase 3: Intelligent Search Strategy Development**

- **Target Profile Construction**: Define an ideal contact profile based on InHand Networks product characteristics
- **Search Parameter Optimization**: Adjust search conditions based on company scale and industry characteristics
- **Keyword Strategy**:
  - Product-related: IoT, Edge Computing, Industrial Router, Gateway
  - Position-related: Product Manager, Technical Director, Procurement
  - Industry-related: Manufacturing, Energy, Transportation

**Phase 4: Multi-round Intelligent Search**

- **Initial Search**: Search using core keywords in global business databases
- **Dynamic Adjustment**:
  - Too few results (<50 people): Relax position requirements → Lower qualification requirements → Expand geographical scope
  - Too many results (>100 people): Raise position level → Refine technical requirements → Limit geographical area
- **Multi-Dimensional Matching**: Combined search of position + department + qualifications + geography
- **Intelligent Filtering**: Excludes obviously irrelevant positions (such as HR, finance, etc.)

**Phase 5: AI Intelligent Assessment and Ranking**

- **Relevance Scoring**:
  - Position matching (40%): Technical decision-makers > Business decision-makers > Procurement decision-makers
  - Influence assessment (30%): C-Level > VP > Director > Manager
  - Industry experience (20%): IoT/Industrial automation experience > General technical experience
  - Activity score (10%): LinkedIn update frequency, profile completeness
- **Purchase Decision Authority Assessment**:
  - Final decision-makers: CEO, CTO, Technical VP
  - Important influencers: Product Manager, Project Manager, Technical Director
  - Participants: Procurement Manager, Business Development Manager
- **LinkedIn Quality Verification**:
  - Profile completeness check
  - Recent activity time verification
  - Current employment status confirmation

**Phase 6: Data Organization and CRM Synchronization**

- **Information Standardization**: Standardize contact data format
- **Recommendation Reason Generation**: Generate personalized contact suggestions for each contact
- **Priority Annotation**: highest > high > medium > low
- **Field Mapping**: Map external data to Zoho CRM fields
- **Batch Import**: Automatically create contact records and associate with companies

#### Default Search Strategy

**Core Search Logic**

```
IF Company Scale > 1000 people THEN
    Focus Search: Department heads, Project managers
    Keywords: Product Manager, Project Manager, Technical Director
ELSE IF Company Scale 100-1000 people THEN
    Focus Search: Technical leaders, Business leaders
    Keywords: CTO, VP of Engineering, Business Development
ELSE Company Scale < 100 people THEN
    Focus Search: Executive level, Founders
    Keywords: CEO, CTO, Founder, Co-founder
```

**Industry Adaptation Strategy**

- **Manufacturing**: Prioritize production technology, automation, quality management related positions
- **Energy Industry**: Focus on smart grid, distribution automation, energy management
- **Transportation**: Focus on Internet of Vehicles, intelligent transportation, logistics management
- **Retail Industry**: Focus on smart retail, supply chain, digital transformation

### Behind-the-Scenes Workflow (No Action Required)

When you click start, the AI assistant will automatically complete the following work:

1. **Research Target Company**

- Obtain detailed company information: scale, industry, website, etc.
- Understand a company background to prepare for search

2. **Check Existing Contacts**

- View existing contacts for the company in your CRM
- Ensure no duplicate additions

3. **AI Intelligent Search**

- Search for company employees in global business databases
- Develop a search strategy based on our product characteristics
- Usually find 50–100 candidates

4. **Precise Filtering**

- Analyze each candidate's position, experience, LinkedIn activity
- Assess their potential demand for our products
- Filter out 5-20 most valuable contacts

5. **Intelligent Organization**

- Automatically format contact information
- Generate recommendation reasons
- Prepare CRM entry format

6. **Auto-import to CRM**

- Add filtered contacts to your Zoho CRM
- Associate with corresponding companies
- Add AI-generated contact reason notes

---

## 6. Strategy & Details

### How Does AI Choose Contacts for You?

**Company Type Analysis**

- **Large Enterprises (100+ people)**: Focus on department heads, project managers and other middle-level decision-makers
- **Small Enterprises (<100 people)**: Directly target CEO, CTO and other senior management
- **Channel Partner Companies**: Prioritize product managers, sales directors and other business core personnel
- **End User Companies**: Focus on technical leaders, procurement decision-makers

**Contact Value Assessment**

AI will score each contact based on the following criteria:

- **Highest Priority** - Product/Project Leaders: Directly responsible for technology selection and procurement
- **High Priority** - C-Level Executives: Final decision-making authority, high influence
- **Medium Priority** - Technical/Solution Experts: Technical evaluation and advisory authority
- **General Priority** - Procurement/Business Personnel: Participate in a procurement process

**Quality Assurance Mechanism**

How the system ensures recommendation quality:

- **Position Matching Check** - Ensure positions are related to our products
- **LinkedIn Activity Verification** - Prioritize contacts with complete and timely updated profiles
- **Company Confirmation** - Verify contacts actually work at target companies
- **Auto-deduplication** - Will not recommend contacts already in your CRM

**Intelligent Adjustment Function**

The system will automatically adjust based on search results:

- **Too Few Contacts Found**: Automatically relax search criteria to include more position types
- **Too Many Contacts Found**: Automatically tighten criteria to keep only core decision-makers
- **Balanced Results**: Usually provide 5-20 selected contacts to ensure balance of quality and quantity

---

## 7. Custom Search Requirements (Advanced Usage)

### Default Detection vs. Custom Detection Comparison

| Comparison Dimension | Default Detection            | Custom Detection                 |
| -------------------- | ---------------------------- | -------------------------------- |
| **Use Case**         | Regular business development | Special projects, specific needs |
| **Search Scope**     | Full functional coverage     | Precise targeting                |
| **Result Quantity**  | 5-20 comprehensive contacts  | 3-15 highly matched contacts     |
| **Match Accuracy**   | 85% relevance                | 95% relevance                    |
| **Processing Time**  | 2-3 minutes                  | 3-5 minutes                      |

### When to Use Custom Search?

#### Scenario 1: Specific Project Requirements

```
Current Project: Smart Factory Digital Transformation
Custom Requirement: "Find manufacturing digital transformation leaders with smart factory or Industry 4.0 project experience"
```

#### Scenario 2: Industry-Specific Breakthrough

```
Key Industry: New Energy
Custom Requirement: "Find new energy industry project managers responsible for charging stations, energy storage or smart grid projects"
```

### Custom Search Practical Templates

#### Manufacturing Templates

```
Basic Version:
"Find manufacturing technical leaders responsible for production line automation or equipment networking"

Advanced Version:
"Find automotive manufacturing digital transformation managers with MES systems or industrial IoT platform project experience, prioritizing Germany and Japan regions"
```

#### Transportation and Logistics Templates

```
Basic Version:
"Find transportation industry IT leaders with fleet management or logistics informatization experience"

Advanced Version:
"Find large logistics companies' digital leaders with warehouse automation or vehicle networking platform construction experience, focusing on Asia-Pacific region"
```

### How AI Understands Your Custom Requirements

**Key Elements AI Can Identify:**

1. **Position Level**: CEO/CTO/VP/Director/Manager/Specialist
2. **Technical Field**: IoT/Edge Computing/Industrial Automation/Digital Transformation
3. **Industry Background**: Manufacturing/Energy/Transportation/Retail/Finance/Healthcare
4. **Geographic Region**: Asia-Pacific/Europe/North America/Specific Countries/Cities
5. **Project Experience**: Specific technology stack, solutions, project types
6. **Company Scale**: Multinational/Large/SME
7. **Decision Authority**: Final decision-maker/Important influencer/Technical evaluator

**Intelligent Semantic Understanding Examples:**

```
Input: "Find technical experts responsible for factory digitalization"
AI Understanding:
- Position: Technical Director, Digital Manager, IT Supervisor
- Industry: Manufacturing
- Technology: Industry 4.0, Smart Manufacturing, MES Systems
- Decision Authority: Technology selection influencer
```

### Advanced Custom Techniques

#### Technique 1: Exclusion Conditions

```
"Find technical leaders with IoT experience, but not traditional IT operations background"
```

#### Technique 2: Priority Setting

```
"Prioritize CTO and Technical VP, then consider Product Managers and Project Managers"
```

#### Technique 3: Business Scenario Description

```
"We want to promote edge computing gateway products, find decision-makers who can evaluate and purchase such equipment"
```

### Custom Search Notes

**Recommended Practices:**

- Describe specific business scenarios and requirements
- Provide a clear position and industry requirements
- Reasonably set geographical and company scale ranges
- Emphasize the relevant project experience

**Things to Avoid:**

- Overly broad descriptions (like "find all technical personnel")
- Contradictory conditions (like "entry-level CTO")
- Overly narrow restrictions (like "only 2019 project experience")
- Include sensitive or discriminatory requirements

---

## 8. Frequently Asked Questions (FAQ)

### Why can't suitable contacts be found sometimes?

**Possible reasons:**

- The company has limited information in global databases (especially small companies)
- Limited data coverage for the company's region or industry
- Company employees' LinkedIn profiles are generally incomplete

**Solutions:**

- Try using custom search with relaxed filtering criteria
- Check if the company name is correct; sometimes different company name formats yield different results

### Why is the number of returned contacts relatively few?

This is normal! The system prioritizes quality over quantity:

- Automatically excluded contacts already in your CRM
- Only recommends contacts most relevant to InHand Networks products
- Filtered out candidates with incomplete LinkedIn profiles
- Prioritized key personnel with high decision-making influence

**Generally, 5-15 selected contacts are more valuable than 50 low-quality contacts.**

### How long does the search take?

**Normal circumstances:**

- Small companies (<100 people): 1-2 minutes
- Medium companies (100-1000 people): 2-3 minutes
- Large companies (>1000 people): 3-5 minutes

### What if contact information is incorrect?

- **Minor errors**: Manually correct directly in CRM
- **Serious errors**: Contact our technical support, we will improve data quality
- **Contact has left**: This is rare as the system verifies LinkedIn activity

### How to improve search effectiveness?

1. **Ensure accurate company information**: Company name, website and other information in CRM must be accurate
2. **Use custom search reasonably**: Describe specific requirements, don't be too general
3. **Regularly clean CRM**: Remove invalid contacts to improve deduplication effectiveness
4. **Choose appropriate timing**: Avoid special periods like company restructuring, renaming

---
