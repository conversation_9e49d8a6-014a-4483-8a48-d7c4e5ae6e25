import { useLocation, useRouter } from '@tanstack/react-router';
import { Flex } from 'antd';
import React, { lazy, useMemo } from 'react';

const Accounts = lazy(() => import('./accounts/index'));
const Contacts = lazy(() => import('./contacts/index'));

const Manual: React.FC = () => {
  const { pathname } = useLocation();
  const { navigate } = useRouter();

  if (pathname === '/manual') {
    navigate({
      to: '/manual/accounts',
    });
  }
  const content = useMemo(() => {
    if (pathname === '/manual/accounts') {
      return <Accounts />;
    }
    if (pathname === '/manual/contacts') {
      return <Contacts />;
    }
  }, [pathname]);

  if (!content) {
    return null;
  }

  return (
    <Flex
      vertical
      justify={'center'}
      style={{
        overflowX: 'auto',
        padding: '0 16px',
      }}
    >
      {content}
    </Flex>
  );
};

export default Manual;
