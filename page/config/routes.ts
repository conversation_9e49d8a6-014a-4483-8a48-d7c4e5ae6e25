// 定义路由配置的类型，方便代码提示和类型检查
export interface RouteConfig {
  path: string;
  name?: string;
  icon?: string;
  key?: string;
  component?: string; // 组件路径，例如 './views/dashboard'
  redirect?: string;
  routes?: RouteConfig[]; // 嵌套路由
}

const routes: RouteConfig[] = [
  {
    path: '/',
    redirect: '/manual',
  },
  {
    path: '/manual',
    name: 'Manual',
    key: 'manual',
    component: 'Manual',
    routes: [
      {
        path: '/manual',
        redirect: '/manual/accounts',
      },
      {
        path: '/manual/accounts',
        icon: 'BankOutlined',
        name: 'Accounts',
        key: 'accounts',
        component: 'Accounts',
      },
      {
        path: '/manual/contacts',
        icon: 'ContactsOutlined',
        name: 'Contacts',
        key: 'contacts',
        component: 'Contacts',
      },
    ],
  },
  {
    path: '/changelog',
    name: 'Changelog',
    key: 'changelog',
    component: 'ChangeLog',
  },
];

export default routes;
