import type { RootRoute } from '@tanstack/react-router';
import { redirect, Route } from '@tanstack/react-router';
import type { FC } from 'react';
import { createElement } from 'react';
import type { RouteConfig } from './routes.ts';

import * as ViewComponentsMap from '../views'; // 动态导入组件，支持懒加载

/**
 * 将配置式路由转换为 TanStack Router 的路由树
 * @param {RouteConfig[]} routesConfig - 路由配置数组
 * @param {RootRoute | Route} parentRoute - 父级路由实例
 * @returns {Route[]}
 */
export const createRouteTree = (
  routesConfig: RouteConfig[],
  parentRoute: RootRoute | Route,
): Route[] => {
  // @ts-ignore
  return routesConfig.map((routeConfig) => {
    const {
      path,
      component: componentKey,
      redirect: redirectTo,
      routes: childrenRoutes,
      ...meta
    } = routeConfig;

    if (redirectTo) {
      // 处理重定向
      return new Route({
        getParentRoute: () => parentRoute,
        path,
        loader: () => {
          throw redirect({ to: redirectTo });
        },
      });
    }

    const Component = ViewComponentsMap[componentKey as keyof typeof ViewComponentsMap] as FC;

    // 创建 TanStack Router 的路由实例
    const routeInstance = new Route({
      getParentRoute: () => parentRoute,
      path,
      component: () => createElement(Component), // 直接使用导入的组件
      // @ts-ignore
      meta,
    });

    if (childrenRoutes && childrenRoutes.length > 0) {
      // @ts-ignore
      routeInstance.addChildren(createRouteTree(childrenRoutes, routeInstance));
    }

    return routeInstance;
  });
};
