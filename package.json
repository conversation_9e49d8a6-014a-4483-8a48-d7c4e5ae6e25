{"name": "inhand-sales-assistant", "version": "0.7.1", "private": true, "description": "InHand Sales Assistant is a browser extension.", "type": "module", "scripts": {"build": "cross-env NODE_ENV=production wxt build && wxt zip && pnpm run build:edge", "build:edge": "wxt zip -b edge", "build:firefox": "cross-env NODE_ENV=production wxt build -b firefox && wxt zip -b firefox", "build:safari": "cross-env NODE_ENV=production wxt build -b safari && wxt zip -b safari", "clean": "wxt clean", "compile": "tsc --noEmit", "dev": "cross-env NODE_ENV=development ENV=local wxt --port 8000 --mode development", "dev:firefox": "cross-env NODE_ENV=development ENV=local wxt -b firefox --port 8000", "dev:local": "cross-env NODE_ENV=development ENV=local wxt --port 8000 --mode development", "dev:safari": "cross-env NODE_ENV=development ENV=local wxt -b safari --port 8000", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "postinstall": "wxt prepare", "lint": "pnpm run lint:check", "lint:check": "eslint --ext .ts,.tsx . && pnpm run stylelint:check && prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\"", "lint:fix": "eslint --ext .ts,.tsx . --fix && pnpm run stylelint:fix && pnpm run format", "prepare": "wxt prepare && husky", "start": "pnpm run dev", "stylelint:check": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\"", "stylelint:fix": "stylelint \"components/**/*.css\" \"entrypoints/**/*.css\" --fix", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "zip:safari": "wxt zip -b safari"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "dependencies": {"@1natsu/wait-element": "^4.1.2", "@ant-design/cssinjs": "^1.24.0", "@ant-design/icons": "~5.6.1", "@ant-design/pro-components": "^2.8.10", "@ant-design/pro-field": "^3.1.0", "@ant-design/x": "^1.6.0", "@floating-ui/react": "^0.27.16", "@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-popover": "^1.1.15", "@tanstack/react-router": "^1.131.31", "@tiptap/extension-document": "^3.3.0", "@tiptap/extension-heading": "^3.3.0", "@tiptap/extension-highlight": "^3.3.0", "@tiptap/extension-horizontal-rule": "^3.3.0", "@tiptap/extension-image": "^3.3.0", "@tiptap/extension-list": "^3.3.0", "@tiptap/extension-paragraph": "^3.3.0", "@tiptap/extension-subscript": "^3.3.0", "@tiptap/extension-superscript": "^3.3.0", "@tiptap/extension-text-align": "^3.3.0", "@tiptap/extension-typography": "^3.3.0", "@tiptap/extensions": "^3.3.0", "@tiptap/pm": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@types/markdown-it": "^14.1.2", "@wxt-dev/storage": "^1.1.1", "@wxt-dev/webextension-polyfill": "^1.0.0", "ahooks": "^3.9.5", "antd": "^5.27.1", "antd-style": "^3.7.1", "axios": "^1.11.0", "dayjs": "^1.11.18", "es-toolkit": "^1.39.10", "lodash.throttle": "^4.1.1", "loglevel": "^1.9.2", "markdown-it": "^14.1.0", "markdown-it-external-link": "^1.1.0", "minimatch": "^10.0.3", "mitt": "^3.0.1", "nlcst-to-string": "^4.0.0", "psl": "^1.15.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hotkeys-hook": "^5.1.0", "react-markdown": "^10.1.0", "react-markdown-typewriter": "^1.1.4", "react-simple-typewriter": "^5.0.1", "react-use": "^17.6.0", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@babel/eslint-parser": "^7.28.0", "@babel/eslint-plugin": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@types/chrome": "^0.1.4", "@types/lodash.throttle": "^4.1.9", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "@vitejs/plugin-react": "^5.0.2", "@wxt-dev/auto-icons": "^1.1.0", "@wxt-dev/module-react": "^1.1.3", "cross-env": "^10.0.0", "eslint": "^9.34.0", "eslint-config-next": "^15.5.2", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jest": "^29.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-you-might-not-need-an-effect": "^0.4.4", "eslint-plugin-unicorn": "^60.0.0", "globals": "^16.3.0", "husky": "^9.1.7", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "prettier-plugin-packagejson": "^2.5.19", "sass": "^1.92.0", "sass-embedded": "^1.92.0", "stylelint": "^16.23.1", "stylelint-config-css-modules": "^4.5.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^39.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.8.0", "stylelint-prettier": "^5.0.3", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0", "vite-plugin-svgr": "^4.5.0", "wxt": "^0.20.8", "zod": "^4.1.5"}, "packageManager": "pnpm@10.15.0", "engines": {"node": ">=20.0.0", "pnpm": ">=10.11.0"}}